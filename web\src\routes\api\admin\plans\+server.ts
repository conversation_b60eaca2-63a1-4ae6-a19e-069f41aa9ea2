import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';
import type { RequestHandler } from './$types';
import type { PlanTier, FeatureAccessLevel } from '$lib/models/features/types';
import {
  syncPlanWithStripeAndUpdateDb,
  getPlanById,
  getPlansFromDatabase,
  initializePlansInDatabase,
  syncPlan,
} from '$lib/server/plan-sync';

export const GET: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('auth_token');

  if (!token) return new Response('Unauthorized', { status: 401 });

  const userData = await verifySessionToken(token);
  if (!userData?.id) return new Response('Unauthorized', { status: 401 });

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user || (!user.isAdmin && user.role !== 'admin')) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // Check if the tables exist and initialize if needed
    await initializePlansInDatabase();

    // Get all plans from the database with fallback to static plans
    const plans = await getPlansFromDatabase();

    return json(plans);
  } catch (error) {
    console.error('Error loading plans:', error);
    return new Response(`Failed to load plans: ${error.message}`, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ cookies, request }) => {
  const token = cookies.get('auth_token');

  if (!token) return new Response('Unauthorized', { status: 401 });

  const userData = await verifySessionToken(token);
  if (!userData?.id) return new Response('Unauthorized', { status: 401 });

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user || (!user.isAdmin && user.role !== 'admin')) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // Check if the tables exist and initialize if needed
    await initializePlansInDatabase();

    const requestData = await request.json();
    const { action, syncWithStripe } = requestData;

    // Handle sync action
    if (action === 'sync_all') {
      // Get all plans
      const plans = await getPlansFromDatabase();

      // Sync each plan individually
      for (const plan of plans) {
        await syncPlan(plan);

        // Sync with Stripe if requested
        if (syncWithStripe && (plan.monthlyPrice > 0 || plan.annualPrice > 0)) {
          await syncPlanWithStripeAndUpdateDb(plan.id);
        }
      }

      return json({ message: 'Plans synced successfully' });
    } else if (action === 'sync_with_stripe' && requestData.planId) {
      // Sync a specific plan with Stripe
      const updatedPlan = await syncPlanWithStripeAndUpdateDb(requestData.planId);
      return json({ plan: updatedPlan, message: 'Plan synced with Stripe successfully' });
    }

    // Handle single plan update
    if (action === 'update_plan') {
      const { plan } = requestData;

      if (!plan || !plan.id) {
        return new Response('Invalid plan data', { status: 400 });
      }

      // Update the single plan using the syncPlan function
      const updatedPlan = await syncPlan(plan);
      return json({ success: true, plan: updatedPlan, message: 'Plan updated successfully' });
    }

    // Handle single feature update
    if (action === 'update_feature') {
      const { planId, featureId, accessLevel, limits } = requestData;

      if (!planId || !featureId || !accessLevel) {
        return new Response('Invalid feature data', { status: 400 });
      }

      try {
        // Get the plan
        const plan = await prisma.plan.findUnique({
          where: { id: planId },
          include: {
            features: {
              include: {
                limits: true,
              },
            },
          },
        });

        if (!plan) {
          return new Response(`Plan not found: ${planId}`, { status: 404 });
        }

        // Find the feature in the plan
        const existingFeature = plan.features.find((f) => f.featureId === featureId);

        if (existingFeature) {
          // Update existing feature
          await prisma.planFeature.update({
            where: { id: existingFeature.id },
            data: { accessLevel },
          });

          // Handle limits
          if (accessLevel === 'limited' && limits) {
            // Update or create limits
            for (const limit of limits) {
              const existingLimit = existingFeature.limits.find((l) => l.limitId === limit.limitId);

              if (existingLimit) {
                // Update existing limit
                await prisma.planFeatureLimit.update({
                  where: { id: existingLimit.id },
                  data: { value: limit.value.toString() },
                });
              } else {
                // Create new limit
                await prisma.planFeatureLimit.create({
                  data: {
                    planFeatureId: existingFeature.id,
                    limitId: limit.limitId,
                    value: limit.value.toString(),
                  },
                });
              }
            }

            // Remove limits that are no longer needed
            const limitIdsToKeep = limits.map((l) => l.limitId);
            for (const existingLimit of existingFeature.limits) {
              if (!limitIdsToKeep.includes(existingLimit.limitId)) {
                await prisma.planFeatureLimit.delete({
                  where: { id: existingLimit.id },
                });
              }
            }
          } else {
            // If the feature is not limited, remove all limits
            await prisma.planFeatureLimit.deleteMany({
              where: { planFeatureId: existingFeature.id },
            });
          }
        } else {
          // Create new feature
          const newFeature = await prisma.planFeature.create({
            data: {
              planId,
              featureId,
              accessLevel,
            },
          });

          // If the feature is limited, create limits
          if (accessLevel === 'limited' && limits) {
            for (const limit of limits) {
              await prisma.planFeatureLimit.create({
                data: {
                  planFeatureId: newFeature.id,
                  limitId: limit.limitId,
                  value: limit.value.toString(),
                },
              });
            }
          }
        }

        // Get the updated plan
        const updatedPlan = await prisma.plan.findUnique({
          where: { id: planId },
          include: {
            features: {
              include: {
                limits: true,
              },
            },
          },
        });

        return json({
          success: true,
          plan: updatedPlan,
          message: `Feature ${featureId} updated successfully for plan ${planId}`,
        });
      } catch (error) {
        console.error('Error updating feature:', error);
        return new Response(`Failed to update feature: ${error.message}`, { status: 500 });
      }
    }

    // Handle multiple plans update
    const { plans } = requestData;

    if (!Array.isArray(plans)) {
      return new Response('Invalid plans data', { status: 400 });
    }

    // Save each plan to the database
    for (const plan of plans) {
      console.log(`Updating plan: ${plan.id}`);

      // Update the plan
      await prisma.plan.update({
        where: { id: plan.id },
        data: {
          name: plan.name,
          description: plan.description,
          section: plan.section,
          monthlyPrice: plan.monthlyPrice,
          annualPrice: plan.annualPrice,
          stripePriceMonthlyId: plan.stripePriceMonthlyId,
          stripePriceYearlyId: plan.stripePriceYearlyId,
          popular: plan.popular || false,
        },
      });

      // Get existing plan features
      const existingPlanFeatures = await prisma.planFeature.findMany({
        where: { planId: plan.id },
        include: { limits: true },
      });

      // Process each feature in the plan
      for (const planFeature of plan.features) {
        const existingPlanFeature = existingPlanFeatures.find(
          (f) => f.featureId === planFeature.featureId
        );

        if (existingPlanFeature) {
          // Update existing plan feature
          await prisma.planFeature.update({
            where: { id: existingPlanFeature.id },
            data: { accessLevel: planFeature.accessLevel },
          });

          // If the feature is limited, update or create limits
          if (planFeature.accessLevel === 'limited' && planFeature.limits) {
            for (const limitValue of planFeature.limits) {
              const existingLimit = existingPlanFeature.limits.find(
                (l) => l.limitId === limitValue.limitId
              );

              if (existingLimit) {
                // Update existing limit
                await prisma.planFeatureLimit.update({
                  where: { id: existingLimit.id },
                  data: { value: limitValue.value.toString() },
                });
              } else {
                // Create new limit
                await prisma.planFeatureLimit.create({
                  data: {
                    planFeatureId: existingPlanFeature.id,
                    limitId: limitValue.limitId,
                    value: limitValue.value.toString(),
                  },
                });
              }
            }

            // Remove limits that are no longer needed
            const limitIdsToKeep = planFeature.limits.map((l) => l.limitId);
            const limitsToRemove = existingPlanFeature.limits.filter(
              (l) => !limitIdsToKeep.includes(l.limitId)
            );

            for (const limitToRemove of limitsToRemove) {
              await prisma.planFeatureLimit.delete({
                where: { id: limitToRemove.id },
              });
            }
          } else {
            // If the feature is not limited, remove all limits
            for (const limitToRemove of existingPlanFeature.limits) {
              await prisma.planFeatureLimit.delete({
                where: { id: limitToRemove.id },
              });
            }
          }
        } else {
          // Create new plan feature
          const newPlanFeature = await prisma.planFeature.create({
            data: {
              planId: plan.id,
              featureId: planFeature.featureId,
              accessLevel: planFeature.accessLevel,
            },
          });

          // If the feature is limited, create limits
          if (planFeature.accessLevel === 'limited' && planFeature.limits) {
            for (const limitValue of planFeature.limits) {
              await prisma.planFeatureLimit.create({
                data: {
                  planFeatureId: newPlanFeature.id,
                  limitId: limitValue.limitId,
                  value: limitValue.value.toString(),
                },
              });
            }
          }
        }
      }

      // Remove features that are no longer in the plan
      const featureIdsToKeep = plan.features.map((f) => f.featureId);
      const featuresToRemove = existingPlanFeatures.filter(
        (f) => !featureIdsToKeep.includes(f.featureId)
      );

      for (const featureToRemove of featuresToRemove) {
        // Delete all limits for this feature
        await prisma.planFeatureLimit.deleteMany({
          where: { planFeatureId: featureToRemove.id },
        });

        // Delete the feature
        await prisma.planFeature.delete({
          where: { id: featureToRemove.id },
        });
      }
    }

    return json({ success: true });
  } catch (error) {
    console.error('Error saving plans:', error);
    return new Response(`Failed to save plans: ${error.message}`, { status: 500 });
  }
};
