<script lang="ts">
  import { superForm } from 'sveltekit-superforms';
  import SuperDebug from 'sveltekit-superforms/client/SuperDebug.svelte';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { toast } from 'svelte-sonner';
  import { Bell, Mail, Megaphone, Monitor } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import SEO from '$components/shared/SEO.svelte';

  // Import tab content components
  import EmailNotifications from './components/EmailNotifications.svelte';
  import JobNotifications from './components/JobNotifications.svelte';
  import MarketingNotifications from './components/MarketingNotifications.svelte';
  import PlatformNotifications from './components/PlatformNotifications.svelte';

  export let data;

  // Define tabs array for easier management
  const tabs = [
    { id: 'email', label: 'Email', icon: Mail },
    { id: 'jobs', label: 'Job Alerts', icon: Bell },
    { id: 'marketing', label: 'Marketing', icon: Megaphone },
    { id: 'platform', label: 'Platform', icon: Monitor },
  ];

  // State variables
  let activeTab = 'email';
  let autoSaveTimeout: NodeJS.Timeout | null = null;
  let autoSaving = false;
  let saveStatus = 'saved'; // 'saved', 'saving', or 'error'
  let statusTimeout: NodeJS.Timeout | null = null;
  let showDebug = false;
  let showResetConfirmation = false;

  // Settings are now defined in the child components

  // Initialize form with auto-save
  const form = superForm(data.form, {
    dataType: 'json',
    validationMethod: 'auto',
    taintedMessage: false, // Disable the browser's "unsaved changes" warning
    onUpdated: ({ form }) => {
      if (form.valid) {
        updateStatus('saved');
        toast.success('Notification settings auto-saved successfully');
      }
    },
    onError: () => {
      updateStatus('error');
      toast.error('Failed to update notification settings');
    },
  });

  const { form: formData, enhance, submitting, delayed } = form;

  // Auto-save functionality with debounce
  function handleFormChange() {
    console.log('Form changed, triggering auto-save');
    updateStatus('saving');
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(async () => {
      if (!$submitting && !autoSaving) {
        autoSaving = true;
        console.log('Auto-saving form...');

        try {
          // Use the enhance function to submit the form
          const formElement = document.getElementById('notification-form') as HTMLFormElement;
          if (formElement) {
            formElement.requestSubmit();
            console.log('Form auto-saved via form element');
          } else {
            console.error('Form element not found');
          }
        } catch (error) {
          console.error('Error auto-saving form:', error);
          updateStatus('error');
        }

        setTimeout(() => {
          autoSaving = false;
        }, 1000);
      }
    }, 1000); // 1 second debounce
  }

  // Function to update status with auto-reset
  function updateStatus(status: string, duration = 3000) {
    saveStatus = status;
    clearTimeout(statusTimeout);
    if (status === 'error') {
      statusTimeout = setTimeout(() => {
        saveStatus = 'saved';
      }, duration);
    }
  }

  // Function to reset notification settings to defaults
  function resetToDefaults() {
    const defaultSettings = {
      emailNotifications: true,
      emailDigest: 'daily' as 'daily' | 'weekly' | 'never',
      emailFormat: 'html' as 'html' | 'text',
      jobMatchNotifications: true,
      jobMatchFrequency: 'daily' as 'realtime' | 'daily' | 'weekly',
      applicationStatusNotifications: true,
      newJobsNotifications: true,
      newJobsFrequency: 'daily' as 'realtime' | 'daily' | 'weekly',
      interviewReminders: true,
      savedJobsUpdates: true,
      jobEmailNotifications: true,
      jobBrowserNotifications: true,
      jobMobileNotifications: false,
      marketingEmails: true,
      productUpdates: true,
      newsletterSubscription: false,
      eventInvitations: false,
      browserNotifications: true,
      desktopNotifications: false,
      mobileNotifications: false,
      pushNotifications: true,
    };

    formData.update(() => defaultSettings);
    toast.success('Notification settings reset to defaults');
    showResetConfirmation = false;

    // Trigger form submission to save the reset settings
    const formElement = document.getElementById('notification-form') as HTMLFormElement;
    if (formElement) {
      formElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }
</script>

<SEO
  title="Notification Settings - Hirli"
  description="Manage your notification preferences for emails, job alerts, marketing communications, and platform notifications."
  keywords="notification settings, email preferences, job alerts, marketing preferences, push notifications"
  url="https://hirli.com/dashboard/settings/notifications" />

<div class="space-y-6">
  <div class="border-border flex flex-col justify-between border-b p-6">
    <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div>
        <div class="flex items-center gap-2">
          <h2 class="text-lg font-semibold">Notification Settings</h2>
          <!-- Removed notification count badge -->
        </div>
        <p class="text-muted-foreground text-sm">
          Manage your notification preferences for emails, job alerts, marketing communications, and
          platform notifications.
        </p>
      </div>
      <div class="flex flex-wrap items-center gap-3">
        <div class="flex items-center gap-2">
          <div
            class="h-2 w-2 rounded-full {saveStatus === 'saving' || $delayed
              ? 'animate-pulse bg-orange-500'
              : saveStatus === 'error'
                ? 'bg-red-500'
                : 'bg-green-500'}">
          </div>
          <span class="text-muted-foreground text-xs">
            {#if saveStatus === 'saving' || $delayed}
              Saving...
            {:else if saveStatus === 'error'}
              Error saving
            {:else if saveStatus === 'saved'}
              Changes saved successfully
            {:else}
              Changes will save automatically
            {/if}
          </span>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 gap-6 md:grid-cols-[1fr_300px]">
    <div>
      <form
        id="notification-form"
        method="POST"
        use:enhance
        on:change={handleFormChange}
        class="space-y-8">
        <Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)}>
          <Tabs.List class="w-full">
            {#each tabs as tab}
              <Tabs.Trigger value={tab.id} class="flex-1">
                <div class="flex items-center gap-2">
                  <svelte:component this={tab.icon} class="h-4 w-4" />
                  <span>{tab.label}</span>
                </div>
              </Tabs.Trigger>
            {/each}
          </Tabs.List>

          <!-- Tab Content -->
          {#each tabs as tab}
            <Tabs.Content value={tab.id} class="mt-6">
              <div class="space-y-6">
                <div class="border-b pb-4">
                  {#if tab.id === 'email'}
                    <div class="flex items-center justify-between">
                      <div>
                        <h3 class="text-lg font-semibold">Email Notifications</h3>
                        <p class="text-muted-foreground text-sm">
                          Configure how you receive email notifications.
                        </p>
                      </div>
                      <Button variant="outline" onclick={() => goto('/dashboard/settings/account')}>
                        Account Settings
                      </Button>
                    </div>
                  {:else if tab.id === 'jobs'}
                    <h3 class="text-lg font-semibold">Job Alert Notifications</h3>
                    <p class="text-muted-foreground text-sm">
                      Configure notifications for job-related activities.
                    </p>
                  {:else if tab.id === 'marketing'}
                    <h3 class="text-lg font-semibold">Marketing Communications</h3>
                    <p class="text-muted-foreground text-sm">
                      Configure marketing and promotional communications.
                    </p>
                  {:else if tab.id === 'platform'}
                    <h3 class="text-lg font-semibold">Platform Notifications</h3>
                    <p class="text-muted-foreground text-sm">
                      Configure how you receive notifications on the platform.
                    </p>
                  {/if}
                </div>
                <div class="space-y-6">
                  {#if tab.id === 'email'}
                    <EmailNotifications formData={form} />
                  {:else if tab.id === 'jobs'}
                    <JobNotifications formData={form} />
                  {:else if tab.id === 'marketing'}
                    <MarketingNotifications formData={form} />
                  {:else if tab.id === 'platform'}
                    <PlatformNotifications formData={form} />
                  {/if}
                </div>
              </div>
            </Tabs.Content>
          {/each}
        </Tabs.Root>

        <!-- Hidden submit button for auto-save functionality -->
        <button id="submit-button" type="submit" class="hidden" aria-label="Save settings"></button>

        <!-- Action buttons -->
        <div class="flex justify-end">
          <Button
            type="button"
            variant="outline"
            onclick={() => (showResetConfirmation = true)}
            disabled={$submitting || $delayed}>
            Reset to Defaults
          </Button>
        </div>
      </form>
    </div>

    {#if showDebug}
      <div class="bg-muted rounded-lg border p-4">
        <h3 class="mb-2 text-sm font-medium">Form Debug</h3>
        <SuperDebug data={$formData} />
      </div>
    {/if}
  </div>
</div>

<!-- Reset Confirmation Dialog -->
<AlertDialog.Root
  open={showResetConfirmation}
  onOpenChange={(open) => (showResetConfirmation = open)}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Reset Notification Settings</AlertDialog.Title>
      <AlertDialog.Description>
        This will reset all notification settings to their default values. This action cannot be
        undone.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={resetToDefaults}>Reset</AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
