<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { goto } from '$app/navigation';
  import {
    User,
    Shield,
    CreditCard,
    Users,
    Bell,
    BellRing,
    Activity,
    Sparkles,
    Share2,
    ChevronRight,
  } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { getStores } from '$app/stores';
  const { page } = getStores();

  // Get user data from the page store
  $: userData = $page.data.user;
  $: hasTeamAccess = userData?.teamId || userData?.hasTeamFeature || false;

  // Define base setting cards that are always shown
  const baseSettingCards = [
    {
      title: 'Profile',
      description: 'Manage your personal information',
      content: 'Update your name, email, profile picture and other personal details.',
      icon: User,
      href: '/dashboard/settings/profile',
    },
    {
      title: 'Account',
      description: 'Manage your account preferences',
      content:
        'Update your notification preferences, language settings, and accessibility options.',
      icon: Bell,
      href: '/dashboard/settings/account',
    },
    {
      title: 'Security',
      description: 'Manage your security settings',
      content: 'Update your password, enable two-factor authentication, and manage your sessions.',
      icon: Shield,
      href: '/dashboard/settings/security',
    },
    {
      title: 'AI Coach',
      description: 'Practice for your interviews',
      content: 'Use AI to practice for your interviews with personalized feedback and suggestions.',
      icon: Sparkles,
      href: '/dashboard/settings/interview-coach',
    },
    {
      title: 'Billing',
      description: 'Manage your subscription and payments',
      content: 'View your current subscription, payment methods, and billing history.',
      icon: CreditCard,
      href: '/dashboard/settings/billing',
    },
    {
      title: 'Usage',
      description: 'Monitor your feature usage',
      content: 'Track your feature usage and subscription limits across the platform.',
      icon: Activity,
      href: '/dashboard/settings/usage',
    },
    {
      title: 'Notifications',
      description: 'Manage your notification preferences',
      content: 'Control how and when you receive notifications across email, browser, and more.',
      icon: BellRing,
      href: '/dashboard/settings/notifications',
    },
    {
      title: 'Referrals',
      description: 'Share and earn rewards',
      content: 'Invite friends to join Hirli and earn rewards for successful referrals.',
      icon: Share2,
      href: '/dashboard/settings/referrals',
    },
  ];

  // Team card - only shown if user has team access
  const teamCard = {
    title: 'Team',
    description: 'Manage your team members',
    content: 'Invite team members, manage permissions, and organize your team.',
    icon: Users,
    href: '/dashboard/settings/team',
  };

  // Computed setting cards based on user permissions
  $: settingCards = hasTeamAccess ? [...baseSettingCards, teamCard] : baseSettingCards;
</script>

<SEO
  title="Account Settings | Hirli"
  description="Manage your Hirli account settings, including profile information, security preferences, and notification settings."
  keywords="account settings, profile settings, security settings, notification preferences, account management" />

<div class="flex h-full flex-col">
  <div class="border-border flex flex-col justify-between border-b p-6">
    <h2 class="text-lg font-semibold">General Settings</h2>
    <p class="text-muted-foreground">Manage your account settings and preferences.</p>
  </div>
  <div class="divide-y">
    {#each settingCards as { title, description, content, icon, href }}
      <div class="hover:bg-muted/50 flex items-center justify-between p-6 transition-colors">
        <div class="flex items-center gap-4">
          <div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
            <svelte:component this={icon} class="text-primary h-5 w-5" />
          </div>
          <div class="space-y-1">
            <h3 class="font-medium">{title}</h3>
            <p class="text-muted-foreground text-sm">{description}</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" onclick={() => goto(href)}>Manage</Button>
          <ChevronRight class="text-muted-foreground h-4 w-4" />
        </div>
      </div>
    {/each}
  </div>
</div>
