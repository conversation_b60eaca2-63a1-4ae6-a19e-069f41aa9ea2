// Service Worker for Push Notifications
console.log('🔧 Service Worker script loaded');

// Install event - simplified to avoid hanging
self.addEventListener('install', (event) => {
  console.log('✅ Service Worker installing...');
  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Activate event - simplified
self.addEventListener('activate', (event) => {
  console.log('✅ Service Worker activating...');
  // Claim all clients immediately
  event.waitUntil(self.clients.claim());
  console.log('✅ Service Worker activated and claimed clients');
});

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);

  let notificationData = {
    title: 'Auto Apply',
    body: 'You have a new notification',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: 'auto-apply-notification',
    data: {
      url: '/dashboard/notifications',
      timestamp: Date.now(),
    },
  };

  // Parse push data if available
  if (event.data) {
    try {
      const data = event.data.json();
      console.log('Push data received:', data);

      notificationData = {
        title: data.title || notificationData.title,
        body: data.message || data.body || notificationData.body,
        icon: data.icon || notificationData.icon,
        badge: data.badge || notificationData.badge,
        tag: data.tag || `notification-${data.id || Date.now()}`,
        data: {
          url: data.url || notificationData.data.url,
          notificationId: data.id,
          timestamp: Date.now(),
          ...data.data,
        },
      };
    } catch (error) {
      console.error('Error parsing push data:', error);
      // Use default notification data if parsing fails
    }
  }

  // Show the notification
  const showNotificationPromise = self.registration.showNotification(notificationData.title, {
    body: notificationData.body,
    icon: notificationData.icon,
    badge: notificationData.badge,
    tag: notificationData.tag,
    data: notificationData.data,
    requireInteraction: false,
    silent: false,
    vibrate: [200, 100, 200],
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/favicon.ico',
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
      },
    ],
  });

  event.waitUntil(showNotificationPromise);
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Default action or 'view' action
  const urlToOpen = event.notification.data?.url || '/dashboard/notifications';

  event.waitUntil(
    clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        console.log('Found clients:', clientList.length);

        // Check if there's already a window/tab open with the target URL or base domain
        for (const client of clientList) {
          const clientUrl = new URL(client.url);
          const targetUrl = new URL(urlToOpen, self.location.origin);

          // If same origin, focus the existing window and navigate to the target URL
          if (clientUrl.origin === targetUrl.origin && 'focus' in client) {
            console.log('Focusing existing client and navigating to:', urlToOpen);
            client.focus();
            // Navigate to the specific URL if it's different
            if (client.url !== urlToOpen) {
              return client.navigate(urlToOpen);
            }
            return client;
          }
        }

        // If no existing window/tab, open a new one
        if (clients.openWindow) {
          console.log('Opening new window for:', urlToOpen);
          return clients.openWindow(urlToOpen);
        }
      })
      .catch((error) => {
        console.error('Error handling notification click:', error);
      })
  );
});

// Background sync (optional - for offline functionality)
self.addEventListener('sync', (event) => {
  console.log('Background sync event:', event.tag);

  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync tasks here
      Promise.resolve()
    );
  }
});

// Simplified service worker - no fetch event handling to avoid conflicts
