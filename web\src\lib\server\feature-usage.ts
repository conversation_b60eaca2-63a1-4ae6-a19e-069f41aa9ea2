import { prisma } from './prisma';
import { FEATURES, FEATURE_LIMITS } from '$lib/models/features';
import type { PlanTier, UserSubscription } from '$lib/models/features/types';
import { FeatureAccessLevel } from '$lib/models/features/features';

/**
 * Check if feature usage tables exist in the database
 * @returns A promise that resolves to true if the tables exist
 */
export async function featureTablesExist(): Promise<boolean> {
  try {
    // Check if the feature table exists
    try {
      await prisma.feature.findFirst({
        select: { id: true },
        take: 1,
      });
    } catch {
      console.log('Feature table does not exist');
      return false;
    }

    // Check if the feature_limit table exists
    try {
      await prisma.featureLimit.findFirst({
        select: { id: true },
        take: 1,
      });
    } catch {
      console.log('FeatureLimit table does not exist');
      return false;
    }

    // Check if the feature_usage table exists
    try {
      await prisma.featureUsage.findFirst({
        select: { id: true },
        take: 1,
      });
      return true;
    } catch (error) {
      console.log('FeatureUsage table does not exist', error);
      return false;
    }
  } catch (error) {
    console.error('Error checking if feature tables exist:', error);
    return false;
  }
}

/**
 * Increment feature usage for a user
 * @param userId The user ID
 * @param featureId The feature ID
 * @param limitId The limit ID
 * @param amount The amount to increment (default: 1)
 * @returns The updated feature usage
 */
export async function incrementFeatureUsage(
  userId: string,
  featureId: string,
  limitId: string,
  amount: number = 1
): Promise<any> {
  return trackFeatureUsage(userId, featureId, limitId, amount);
}

/**
 * Track feature usage for a user
 * @param userId The user ID
 * @param featureId The feature ID
 * @param limitId The limit ID
 * @param amount The amount to increment (default: 1)
 * @returns The updated feature usage
 */
export async function trackFeatureUsage(
  userId: string,
  featureId: string,
  limitId: string,
  amount: number = 1
): Promise<any> {
  // Check if the tables exist
  const tablesExist = await featureTablesExist();
  if (!tablesExist) {
    console.warn('Feature usage tables do not exist yet');
    return {
      id: 'temp',
      userId,
      featureId,
      limitId,
      used: amount,
      period: null,
      updatedAt: new Date(),
    };
  }

  // Get the feature and limit to determine the period
  const featureLimit = await prisma.featureLimit.findFirst({
    where: {
      id: limitId,
      featureId: featureId,
    },
  });

  if (!featureLimit) {
    // Try to get the limit from the registry
    const limit = FEATURE_LIMITS[limitId];
    if (!limit) {
      throw new Error(`Feature limit not found: ${limitId}`);
    }

    // Use the limit from the registry
    return handleFeatureUsageWithRegistry(userId, featureId, limitId, amount, limit);
  }

  // Determine the period based on the limit type
  let period: string | null = null;
  if (featureLimit.type === 'monthly') {
    const now = new Date();
    period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  } else if (featureLimit.type === 'yearly') {
    const now = new Date();
    period = `${now.getFullYear()}`;
  }

  // Check if the user has a usage record for this feature and period
  let usage = await prisma.featureUsage.findFirst({
    where: {
      userId,
      featureId,
      limitId,
      period,
    },
  });

  // Create or update the usage record
  if (usage) {
    usage = await prisma.featureUsage.update({
      where: { id: usage.id },
      data: {
        used: usage.used + amount,
        updatedAt: new Date(),
      },
    });
  } else {
    usage = await prisma.featureUsage.create({
      data: {
        userId,
        featureId,
        limitId,
        used: amount,
        period,
      },
    });
  }

  return usage;
}

/**
 * Handle feature usage with registry data when the database tables are not fully set up
 * @param userId The user ID
 * @param featureId The feature ID
 * @param limitId The limit ID
 * @param amount The amount to increment
 * @param limit The limit from the registry
 * @returns The updated feature usage
 */
async function handleFeatureUsageWithRegistry(
  userId: string,
  featureId: string,
  limitId: string,
  amount: number,
  limit: any
): Promise<any> {
  // Determine the period based on the limit type
  let period: string | null = null;
  if (limit.type === 'monthly') {
    const now = new Date();
    period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  } else if (limit.type === 'yearly') {
    const now = new Date();
    period = `${now.getFullYear()}`;
  }

  // Try to create the feature and limit if they don't exist
  try {
    // Check if the feature exists
    const feature = await prisma.feature.findUnique({
      where: { id: featureId },
    });

    if (!feature) {
      // Get the feature from the registry
      const registryFeature = FEATURES.find((f) => f.id === featureId);
      if (registryFeature) {
        // Create the feature
        await prisma.feature.create({
          data: {
            id: registryFeature.id,
            name: registryFeature.name,
            description: registryFeature.description || '',
            category: registryFeature.category || 'general',
            icon: registryFeature.icon || null,
            beta: registryFeature.beta || false,
            updatedAt: new Date(),
          },
        });
      }
    }

    // Check if the limit exists
    const featureLimit = await prisma.featureLimit.findUnique({
      where: { id: limitId },
    });

    if (!featureLimit) {
      // Create the limit
      await prisma.featureLimit.create({
        data: {
          id: limitId,
          featureId: featureId,
          name: limit.name,
          description: limit.description ?? '',
          defaultValue: limit.defaultValue.toString(),
          type: limit.type,
          unit: limit.unit ?? null,
          resetDay: limit.resetDay ?? null,
        },
      });
    }
  } catch (error) {
    console.error('Error creating feature or limit:', error);
  }

  // Check if the user has a usage record for this feature and period
  let usage = await prisma.featureUsage.findFirst({
    where: {
      userId,
      featureId,
      limitId,
      period,
    },
  });

  // Create or update the usage record
  if (usage) {
    usage = await prisma.featureUsage.update({
      where: { id: usage.id },
      data: {
        used: usage.used + amount,
        updatedAt: new Date(),
      },
    });
  } else {
    usage = await prisma.featureUsage.create({
      data: {
        userId,
        featureId,
        limitId,
        used: amount,
        period,
      },
    });
  }

  return usage;
}

/**
 * Check if a user has reached their limit for a feature
 * @param userId The user ID
 * @param featureId The feature ID
 * @param limitId The limit ID
 * @returns Whether the user has reached their limit
 */
export async function hasReachedLimit(
  userId: string,
  featureId: string,
  limitId: string
): Promise<boolean> {
  // In development mode, always return false (no limits)
  if (
    process.env.NODE_ENV === 'development' ||
    process.env.VITE_DISABLE_FEATURE_LIMITS === 'true'
  ) {
    console.log('Development mode: Bypassing feature limit check');
    return false;
  }

  // Check if the tables exist
  const tablesExist = await featureTablesExist();
  if (!tablesExist) {
    console.warn('Feature usage tables do not exist yet');
    return false; // Assume they haven't reached the limit if tables don't exist
  }

  // Get the user's subscription to determine the plan
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      subscriptions: {
        orderBy: { createdAt: 'desc' },
        take: 1,
        include: {
          plan: {
            include: {
              features: {
                include: {
                  limits: true,
                },
              },
            },
          },
        },
      },
    },
  });

  // Get the current plan
  const currentPlan = user?.subscriptions[0]?.plan;
  if (!currentPlan) {
    // If the user doesn't have a plan, assume they've reached the limit
    return true;
  }

  // Find the limit for this feature in the user's plan
  const planFeature = currentPlan.features.find((pf) => pf.featureId === featureId);

  if (!planFeature) {
    // If the feature isn't in the plan, assume they've reached the limit
    return true;
  }

  const planLimit = planFeature.limits.find((pl) => pl.limitId === limitId);

  if (!planLimit) {
    // If the limit isn't in the plan, assume they've reached the limit
    return true;
  }

  // Get the limit value
  const limit = parseInt(planLimit.value, 10);
  if (limit === 0) {
    // If the limit is 0, they've reached the limit
    return true;
  }

  // Get the feature limit to determine the period
  const featureLimit = await prisma.featureLimit.findFirst({
    where: {
      id: limitId,
      featureId: featureId,
    },
  });

  if (!featureLimit) {
    throw new Error(`Feature limit not found: ${limitId}`);
  }

  // Determine the period based on the limit type
  let period: string | null = null;
  if (featureLimit.type === 'monthly') {
    const now = new Date();
    period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  } else if (featureLimit.type === 'yearly') {
    const now = new Date();
    period = `${now.getFullYear()}`;
  }

  // Get the current usage
  const usage = await prisma.featureUsage.findFirst({
    where: {
      userId,
      featureId,
      limitId,
      period,
    },
  });

  // If there's no usage record, they haven't reached the limit
  if (!usage) {
    return false;
  }

  // Check if they've reached the limit
  return usage.used >= limit;
}

/**
 * Get all feature usage for a user
 * @param userId The user ID
 * @returns The user's feature usage
 */
export async function getFeatureUsage(userId: string): Promise<any[]> {
  try {
    // Check if the tables exist
    const tablesExist = await featureTablesExist();
    if (!tablesExist) {
      console.warn('Feature usage tables do not exist yet');
      return []; // Return empty array if tables don't exist
    }

    // Get feature usage from the database
    let featureUsage: any[] = [];

    try {
      // First try to get usage without relations to avoid errors
      const usageRecords = await prisma.featureUsage.findMany({
        where: {
          userId,
        },
      });

      console.log(`Found ${usageRecords.length} usage records`);

      if (usageRecords.length === 0) {
        // No usage records found, but tables exist
        // Return empty array with a special message
        console.log('No usage records found for user, but tables exist');

        // Try to get all features to show them in the UI
        try {
          const features = await prisma.feature.findMany({
            include: {
              limits: true,
            },
          });

          if (features.length > 0) {
            console.log(`Found ${features.length} features to show in UI`);

            // Create placeholder usage records for the UI
            const placeholderUsage = features.flatMap((feature) =>
              feature.limits.map((limit) => ({
                id: `placeholder-${feature.id}-${limit.id}`,
                featureId: feature.id,
                featureName: feature.name,
                limitId: limit.id,
                limitName: limit.name,
                used: 0,
                limit: parseInt(limit.defaultValue, 10) || null,
                remaining: parseInt(limit.defaultValue, 10) || null,
                percentUsed: 0,
                period: new Date().toISOString().substring(0, 7), // YYYY-MM
                updatedAt: new Date(),
                placeholder: true,
              }))
            );

            return placeholderUsage;
          }
        } catch (featuresError) {
          console.error('Error getting features for placeholder usage:', featuresError);
        }

        return []; // No usage records found
      }

      featureUsage = usageRecords;

      // Manually fetch feature and limit data
      const featureIds = [...new Set(featureUsage.map((u: any) => u.featureId))];
      const limitIds = [...new Set(featureUsage.map((u: any) => u.limitId))];

      let features: any[] = [];
      let limits: any[] = [];

      try {
        features = await prisma.feature.findMany({
          where: {
            id: {
              in: featureIds as string[],
            },
          },
        });

        console.log(`Found ${features.length} features`);
      } catch (featureError) {
        console.error('Error fetching features:', featureError);
      }

      try {
        limits = await prisma.featureLimit.findMany({
          where: {
            id: {
              in: limitIds as string[],
            },
          },
        });

        console.log(`Found ${limits.length} limits`);
      } catch (limitError) {
        console.error('Error fetching limits:', limitError);
      }

      // Add feature and limit data to usage records
      featureUsage = featureUsage.map((usage: any) => {
        const feature = features.find((f: any) => f.id === usage.featureId);
        const limit = limits.find((l: any) => l.id === usage.limitId);

        // If feature or limit is not found, try to get it from the registry
        let featureName = 'Unknown Feature';
        let limitName = 'Unknown Limit';

        if (!feature) {
          const registryFeature = FEATURES.find((f) => f.id === usage.featureId);
          if (registryFeature) {
            featureName = registryFeature.name;
          }
        }

        if (!limit) {
          const registryFeature = FEATURES.find((f) => f.id === usage.featureId);
          if (registryFeature?.limits) {
            // Find the limit ID in the feature's limits array
            const limitId = usage.limitId;
            const registryLimitId = registryFeature.limits.find((l: any) =>
              typeof l === 'string' ? l === limitId : l.id === limitId
            );

            if (
              registryLimitId &&
              typeof registryLimitId === 'string' &&
              FEATURE_LIMITS[registryLimitId]
            ) {
              limitName = FEATURE_LIMITS[registryLimitId].name;
            }
          }
        }

        return {
          ...usage,
          feature: feature ?? { name: featureName },
          limit: limit ?? { name: limitName },
        };
      });
    } catch (error) {
      console.error('Error fetching feature usage:', error);
      return []; // Return empty array on error
    }

    // Get the user's subscription to determine the plan
    let currentPlan: any = null;

    try {
      // Get user with subscriptions
      const userWithSubscriptions = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          subscriptions: {
            orderBy: { createdAt: 'desc' },
            take: 1,
            include: {
              plan: true,
            },
          },
        },
      });

      // Get the current plan
      if (userWithSubscriptions?.subscriptions && userWithSubscriptions.subscriptions.length > 0) {
        const subscription = userWithSubscriptions.subscriptions[0];
        if (subscription.plan) {
          // Get plan with features and limits
          const plan = await prisma.plan.findUnique({
            where: { id: subscription.planId },
            include: {
              features: {
                include: {
                  limits: true,
                },
              },
            },
          });

          currentPlan = plan;
        }
      }
    } catch (error) {
      console.error('Error fetching user subscription data:', error);
    }

    // Helper to get limit value from plan or registry
    function getLimitValue(usage: any, currentPlan: any): number | null {
      const planFeature = currentPlan?.features?.find(
        (pf: any) => pf.featureId === usage.featureId
      );
      const planLimit = planFeature?.limits?.find((pl: any) => pl.limitId === usage.limitId);

      if (planLimit) {
        return parseInt(planLimit.value, 10);
      }

      const feature = FEATURES.find((f) => f.id === usage.featureId);
      if (feature?.limits) {
        const limitId = usage.limitId;
        const registryLimitId = feature.limits.find((l: any) =>
          typeof l === 'string' ? l === limitId : l.id === limitId
        );

        if (registryLimitId && typeof registryLimitId === 'string') {
          const featureLimit = FEATURE_LIMITS[registryLimitId];
          if (featureLimit) {
            if (typeof featureLimit.defaultValue === 'number') {
              return featureLimit.defaultValue;
            } else if (featureLimit.defaultValue === 'unlimited') {
              return null;
            } else {
              return parseInt(String(featureLimit.defaultValue), 10);
            }
          }
        }
      }
      return null;
    }

    // Helper to calculate remaining and percentUsed
    function getUsageMetrics(limit: number | null, used: number) {
      const remaining = limit !== null ? Math.max(0, limit - used) : null;
      const percentUsed = limit !== null ? Math.min(100, (used / limit) * 100) : null;
      return { remaining, percentUsed };
    }

    // Format the response
    return featureUsage.map((usage: any) => {
      const limit = getLimitValue(usage, currentPlan);
      const { remaining, percentUsed } = getUsageMetrics(limit, usage.used);

      return {
        id: usage.id,
        featureId: usage.featureId,
        featureName: usage.feature?.name ?? 'Unknown Feature',
        limitId: usage.limitId,
        limitName: usage.limit?.name ?? 'Unknown Limit',
        used: usage.used,
        limit,
        remaining,
        percentUsed,
        period: usage.period,
        updatedAt: usage.updatedAt,
      };
    });
  } catch (error) {
    console.error('Error in getFeatureUsage:', error);
    return []; // Return empty array on error
  }
}

/**
 * Get user feature usage with plan limits
 * This provides a comprehensive view of all features, their usage, and limits based on the user's plan
 * @param userId The user ID
 * @returns A promise that resolves to the user's feature usage with plan limits
 */
export async function getUserFeatureUsageWithPlanLimits(userId: string): Promise<any> {
  try {
    // Check if the tables exist
    const tablesExist = await featureTablesExist();
    if (!tablesExist) {
      console.warn('Feature usage tables do not exist yet');
      return {
        features: [],
        plan: null,
        subscription: null,
      };
    }

    // Get the user with their subscription
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          include: {
            plan: true,
          },
        },
        featureUsage: true,
      },
    });

    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    // Get the current subscription and plan
    const subscription = user.subscriptions[0] || null;
    let plan: PlanTier | null = null;

    if (subscription?.plan) {
      // Get the full plan with features and limits
      const dbPlan = await prisma.plan.findUnique({
        where: { id: subscription.planId },
        include: {
          features: {
            include: {
              limits: true,
            },
          },
        },
      });

      if (dbPlan) {
        // Convert to PlanTier
        plan = {
          id: dbPlan.id,
          name: dbPlan.name,
          description: dbPlan.description,
          section: dbPlan.section as 'pro' | 'teams',
          monthlyPrice: dbPlan.monthlyPrice,
          annualPrice: dbPlan.annualPrice,
          stripePriceMonthlyId: dbPlan.stripePriceMonthlyId || undefined,
          stripePriceYearlyId: dbPlan.stripePriceYearlyId || undefined,
          popular: dbPlan.popular,
          features: dbPlan.features.map((feature) => ({
            featureId: feature.featureId,
            accessLevel: feature.accessLevel as FeatureAccessLevel,
            limits: feature.limits.map((limit) => ({
              limitId: limit.limitId,
              value: limit.value === 'unlimited' ? 'unlimited' : parseInt(limit.value, 10),
            })),
          })),
        };
      }
    }

    // Get all features from the database
    const dbFeatures = await prisma.feature.findMany({
      include: {
        limits: true,
      },
    });

    // Get all usage records for the user
    const usageRecords = user.featureUsage;

    // Create a map of feature usage by feature ID and limit ID
    const usageMap = new Map();
    usageRecords.forEach((usage) => {
      const key = `${usage.featureId}:${usage.limitId}`;
      usageMap.set(key, usage);
    });

    // Map features with usage and plan limits
    const featuresWithUsage = dbFeatures.map((feature) => {
      // Find the feature in the plan
      const planFeature = plan?.features.find((pf) => pf.featureId === feature.id);
      const accessLevel = planFeature?.accessLevel || FeatureAccessLevel.NotIncluded;

      // Map limits with usage
      const limitsWithUsage = feature.limits.map((limit) => {
        // Find the limit in the plan
        const planLimit = planFeature?.limits?.find((pl) => pl.limitId === limit.id);

        // Get the limit value from the plan or use the default
        const limitValue =
          planLimit?.value ||
          (limit.defaultValue === 'unlimited' ? 'unlimited' : parseInt(limit.defaultValue, 10));

        // Find usage for this feature and limit
        const usageKey = `${feature.id}:${limit.id}`;
        const usage = usageMap.get(usageKey);

        // Calculate usage metrics
        const used = usage?.used ?? 0;
        const remaining = typeof limitValue === 'number' ? Math.max(0, limitValue - used) : null;
        const percentUsed =
          typeof limitValue === 'number' ? Math.min(100, (used / limitValue) * 100) : null;

        return {
          id: limit.id,
          name: limit.name,
          description: limit.description,
          type: limit.type,
          unit: limit.unit,
          resetDay: limit.resetDay,
          value: limitValue,
          used,
          remaining,
          percentUsed,
          period: usage?.period ?? null,
          lastUpdated: usage?.updatedAt ?? null,
        };
      });

      return {
        id: feature.id,
        name: feature.name,
        description: feature.description,
        category: feature.category,
        icon: feature.icon,
        beta: feature.beta,
        accessLevel,
        limits: limitsWithUsage,
      };
    });

    // Create subscription object
    let userSubscription: UserSubscription | null = null;
    if (subscription) {
      userSubscription = {
        planId: subscription.planId,
        startDate: subscription.currentPeriodStart || subscription.createdAt,
        endDate: subscription.currentPeriodEnd || undefined,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        status: subscription.status as 'active' | 'canceled' | 'past_due' | 'trialing',
        trialEndDate: undefined, // Add this if available
      };
    }

    return {
      features: featuresWithUsage,
      plan,
      subscription: userSubscription,
    };
  } catch (error) {
    console.error('Error getting user feature usage with plan limits:', error);
    return {
      features: [],
      plan: null,
      subscription: null,
      error: error.message,
    };
  }
}
