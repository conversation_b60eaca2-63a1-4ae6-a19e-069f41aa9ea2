import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';
import { initializeFeatureData } from '$lib/server/initialize-features';
import type { RequestHand<PERSON> } from './$types';
import { FeatureAccessLevel } from '$lib/models/features/features';

/**
 * Synchronizes a feature with all existing plans
 * @param featureId The ID of the feature to sync
 */
async function syncFeatureWithPlans(featureId: string) {
  try {
    // Get all plans
    const plans = await prisma.plan.findMany({
      include: {
        features: {
          include: {
            limits: true,
          },
        },
      },
    });

    if (plans.length === 0) {
      console.log('No plans found to synchronize with.');
      return;
    }

    console.log(`Syncing feature ${featureId} with ${plans.length} plans...`);

    // Define default access levels based on plan tier
    const getDefaultAccessLevel = (planName: string, featureId: string) => {
      const planNameLower = planName.toLowerCase();

      // Core features - available to all plans
      if (
        featureId.startsWith('career_') ||
        featureId === 'privacy_settings' ||
        featureId === 'notification_preferences'
      ) {
        return FeatureAccessLevel.Included;
      }

      // Free plan gets basic features
      if (planNameLower.includes('free')) {
        // Only give free users access to very basic features
        if (
          featureId === 'job_alerts' ||
          featureId === 'resume_builder' ||
          featureId === 'application_tracking'
        ) {
          return FeatureAccessLevel.Limited;
        }
        return FeatureAccessLevel.NotIncluded;
      }

      // Basic/Starter plans get more features but with limits
      if (planNameLower.includes('basic') || planNameLower.includes('starter')) {
        // Advanced features not included
        if (
          featureId.includes('advanced_') ||
          featureId.includes('team_') ||
          featureId.includes('integration_')
        ) {
          return FeatureAccessLevel.NotIncluded;
        }
        // Most features limited
        return FeatureAccessLevel.Limited;
      }

      // Pro/Premium plans get most features unlimited
      if (planNameLower.includes('pro') || planNameLower.includes('premium')) {
        // Some very advanced features still limited
        if (featureId.includes('team_') || featureId === 'custom_automation_workflows') {
          return FeatureAccessLevel.Limited;
        }
        return FeatureAccessLevel.Unlimited;
      }

      // Enterprise/Business plans get everything unlimited
      if (planNameLower.includes('enterprise') || planNameLower.includes('business')) {
        return FeatureAccessLevel.Unlimited;
      }

      // Default for other plans - limited access
      return FeatureAccessLevel.Limited;
    };

    let syncCount = 0;
    let errorCount = 0;

    // For each plan, add the feature with appropriate access level
    for (const plan of plans) {
      try {
        // Check if the plan already has this feature
        const existingFeature = plan.features.find((f) => f.featureId === featureId);

        if (existingFeature) {
          console.log(`Plan ${plan.name} already has feature ${featureId}`);
          continue;
        }

        // Determine the appropriate access level for this plan
        const accessLevel = getDefaultAccessLevel(plan.name, featureId);

        // Add the feature to the plan
        await prisma.planFeature.create({
          data: {
            planId: plan.id,
            featureId: featureId,
            accessLevel: accessLevel,
          },
        });

        console.log(
          `Added feature ${featureId} to plan ${plan.name} with access level: ${accessLevel}`
        );
        syncCount++;
      } catch (error) {
        console.error(`Error adding feature ${featureId} to plan ${plan.name}:`, error);
        errorCount++;
      }
    }

    console.log(
      `Feature synchronization complete! Synchronized with ${syncCount} plans. Errors: ${errorCount}`
    );
  } catch (error) {
    console.error('Error synchronizing feature with plans:', error);
  }
}

export const GET: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('auth_token');

  if (!token) return new Response('Unauthorized', { status: 401 });

  const userData = await verifySessionToken(token);
  if (!userData?.id) return new Response('Unauthorized', { status: 401 });

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user || (!user.isAdmin && user.role !== 'admin')) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // Initialize features if needed
    try {
      await initializeFeatureData();
    } catch (initError) {
      console.warn('Error initializing features:', initError);
      // Continue anyway - we'll try to load features
    }

    // Get all features from the database
    const features = await prisma.feature.findMany({
      include: {
        limits: true,
      },
      orderBy: {
        category: 'asc',
      },
    });

    return json({ features });
  } catch (error) {
    console.error('Error loading features:', error);
    return new Response(`Failed to load features: ${error.message}`, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ cookies, request }) => {
  const token = cookies.get('auth_token');

  if (!token) return new Response('Unauthorized', { status: 401 });

  const userData = await verifySessionToken(token);
  if (!userData?.id) return new Response('Unauthorized', { status: 401 });

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user || (!user.isAdmin && user.role !== 'admin')) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const requestData = await request.json();
    const { action } = requestData;

    // Handle add feature action
    if (action === 'add_feature') {
      const { feature } = requestData;

      if (!feature || !feature.id || !feature.name) {
        return new Response('Invalid feature data', { status: 400 });
      }

      // Check if feature already exists
      const existingFeature = await prisma.feature.findUnique({
        where: { id: feature.id },
      });

      if (existingFeature) {
        return new Response(`Feature with ID ${feature.id} already exists`, { status: 400 });
      }

      // Create the feature with limits if provided
      const newFeature = await prisma.$transaction(async (tx) => {
        // Create the feature first
        const createdFeature = await tx.feature.create({
          data: {
            id: feature.id,
            name: feature.name,
            description: feature.description || '',
            category: feature.category || 'general',
            icon: feature.icon || null,
            beta: feature.beta || false,
            updatedAt: new Date(),
          },
        });

        // If limits are provided, create them
        if (feature.limits && Array.isArray(feature.limits) && feature.limits.length > 0) {
          console.log(`Creating ${feature.limits.length} limits for feature ${feature.id}`);

          for (const limit of feature.limits) {
            await tx.featureLimit.create({
              data: {
                featureId: feature.id,
                id: limit.id,
                name: limit.name,
                description: limit.description || '',
                defaultValue: limit.defaultValue?.toString() || '10',
                type: limit.type || 'monthly',
                unit: limit.unit || null,
                resetDay: limit.resetDay || null,
              },
            });
          }
        }

        return createdFeature;
      });

      // Sync the new feature with all existing plans
      await syncFeatureWithPlans(feature.id);

      return json({
        success: true,
        feature: newFeature,
        message: `Feature ${feature.name} created successfully`,
      });
    }

    // Handle remove feature action
    if (action === 'remove_feature') {
      const { featureId } = requestData;

      if (!featureId) {
        return new Response('Invalid feature ID', { status: 400 });
      }

      // Check if feature exists
      const existingFeature = await prisma.feature.findUnique({
        where: { id: featureId },
      });

      if (!existingFeature) {
        return new Response(`Feature with ID ${featureId} not found`, { status: 404 });
      }

      // First, remove all plan feature limits
      await prisma.$transaction(async (tx) => {
        // Find all plan features for this feature
        const planFeatures = await tx.planFeature.findMany({
          where: { featureId },
          select: { id: true },
        });

        // Delete all plan feature limits for these plan features
        if (planFeatures.length > 0) {
          const planFeatureIds = planFeatures.map((pf) => pf.id);
          await tx.planFeatureLimit.deleteMany({
            where: { planFeatureId: { in: planFeatureIds } },
          });
        }

        // Delete all plan features for this feature
        await tx.planFeature.deleteMany({
          where: { featureId },
        });

        // Delete all feature limits for this feature
        await tx.featureLimit.deleteMany({
          where: { featureId },
        });

        // Delete the feature
        await tx.feature.delete({
          where: { id: featureId },
        });
      });

      return json({
        success: true,
        message: `Feature ${featureId} removed successfully`,
      });
    }

    // Handle update feature action
    if (action === 'update_feature') {
      const { feature } = requestData;

      if (!feature || !feature.id) {
        return new Response('Invalid feature data', { status: 400 });
      }

      // Check if feature exists
      const existingFeature = await prisma.feature.findUnique({
        where: { id: feature.id },
      });

      if (!existingFeature) {
        return new Response(`Feature with ID ${feature.id} not found`, { status: 404 });
      }

      // Update the feature and its limits
      const updatedFeature = await prisma.$transaction(async (tx) => {
        // Update the feature first
        const updated = await tx.feature.update({
          where: { id: feature.id },
          data: {
            name: feature.name,
            description: feature.description || '',
            category: feature.category || 'general',
            icon: feature.icon || null,
            beta: feature.beta || false,
            updatedAt: new Date(),
          },
          include: {
            limits: true,
          },
        });

        // Handle limits if provided
        if (feature.limits && Array.isArray(feature.limits)) {
          console.log(`Updating limits for feature ${feature.id}`);

          // Get existing limits
          const existingLimits = await tx.featureLimit.findMany({
            where: { featureId: feature.id },
          });

          const existingLimitIds = existingLimits.map((limit) => limit.id);
          const newLimitIds = feature.limits.map((limit) => limit.id);

          // Delete limits that are no longer in the feature
          const limitsToDelete = existingLimitIds.filter((id) => !newLimitIds.includes(id));
          if (limitsToDelete.length > 0) {
            await tx.featureLimit.deleteMany({
              where: {
                id: { in: limitsToDelete },
                featureId: feature.id,
              },
            });
          }

          // Update or create limits
          for (const limit of feature.limits) {
            if (existingLimitIds.includes(limit.id)) {
              // Update existing limit
              await tx.featureLimit.update({
                where: {
                  id: limit.id,
                },
                data: {
                  name: limit.name,
                  description: limit.description || '',
                  defaultValue: limit.defaultValue?.toString() || '10',
                  type: limit.type || 'monthly',
                  unit: limit.unit || null,
                  resetDay: limit.resetDay || null,
                },
              });
            } else {
              // Create new limit
              await tx.featureLimit.create({
                data: {
                  id: limit.id,
                  featureId: feature.id,
                  name: limit.name,
                  description: limit.description || '',
                  defaultValue: limit.defaultValue?.toString() || '10',
                  type: limit.type || 'monthly',
                  unit: limit.unit || null,
                  resetDay: limit.resetDay || null,
                },
              });
            }
          }
        }

        // Return the updated feature with limits
        return await tx.feature.findUnique({
          where: { id: feature.id },
          include: {
            limits: true,
          },
        });
      });

      return json({
        success: true,
        feature: updatedFeature,
        message: `Feature ${feature.name} updated successfully`,
      });
    }

    // Handle sync features action
    if (action === 'sync_features') {
      try {
        // Initialize features first
        await initializeFeatureData();

        // Get all features
        const features = await prisma.feature.findMany();

        // Get all plans
        const plans = await prisma.plan.findMany({
          include: {
            features: {
              include: {
                limits: true,
              },
            },
          },
        });

        // Track changes for reporting
        const changes = {
          total: 0,
          byPlan: {},
        };

        // For each plan, ensure it has all features
        for (const plan of plans) {
          // Initialize changes counter for this plan
          changes.byPlan[plan.id] = 0;

          // Get existing feature IDs for this plan
          const existingFeatureIds = plan.features.map((pf) => pf.featureId);

          // Find features that are not assigned to this plan
          const missingFeatures = features.filter(
            (feature) => !existingFeatureIds.includes(feature.id)
          );

          if (missingFeatures.length > 0) {
            // Determine default access level based on plan
            let defaultAccessLevel = 'included';

            // For free plan, set most features to not_included by default
            if (plan.id === 'free' || plan.name.toLowerCase() === 'free') {
              defaultAccessLevel = 'not_included';

              // For free plan, we'll set specific categories to be included or limited
              const includedCategories = ['core'];
              const limitedCategories = ['resume', 'job_search', 'applications'];

              // Specific features that should be included or limited for the free plan
              const includedFeatures = ['application_tracker', 'application_submit'];
              const limitedFeatures = [
                'cover_letter_generator',
                'application_tracking',
                'resume_scanner',
                'resume_builder',
              ];

              // Create feature assignments with appropriate access levels
              const featureAssignments = missingFeatures.map((feature) => {
                let accessLevel = defaultAccessLevel;

                // First check specific features
                if (includedFeatures.includes(feature.id)) {
                  accessLevel = 'included';
                } else if (limitedFeatures.includes(feature.id)) {
                  accessLevel = 'limited';
                }
                // Then check categories if not already set
                else if (includedCategories.includes(feature.category)) {
                  accessLevel = 'included';
                } else if (limitedCategories.includes(feature.category)) {
                  accessLevel = 'limited';
                }

                return {
                  planId: plan.id,
                  featureId: feature.id,
                  accessLevel,
                };
              });

              // Add features to plan
              await prisma.planFeature.createMany({
                data: featureAssignments,
              });

              changes.total += featureAssignments.length;
              changes.byPlan[plan.id] = featureAssignments.length;
            } else {
              // For paid plans, include all features by default
              // Create feature assignments
              const featureAssignments = missingFeatures.map((feature) => ({
                planId: plan.id,
                featureId: feature.id,
                accessLevel: defaultAccessLevel,
              }));

              // Add features to plan
              await prisma.planFeature.createMany({
                data: featureAssignments,
              });

              changes.total += featureAssignments.length;
              changes.byPlan[plan.id] = featureAssignments.length;
            }
          }
        }

        return json({
          success: true,
          message: `Synced features across all plans. Added ${changes.total} missing feature assignments.`,
          changes,
        });
      } catch (error) {
        console.error('Error syncing features:', error);
        return json(
          {
            success: false,
            error: 'Failed to sync features',
            details: error.message,
          },
          { status: 500 }
        );
      }
    }

    return new Response('Invalid action', { status: 400 });
  } catch (error) {
    console.error('Error managing features:', error);
    return new Response(`Failed to manage features: ${error.message}`, { status: 500 });
  }
};
