import { prisma } from './prisma';
import type { PlanTier, Feature, FeatureLimit } from '$lib/models/features/types';
import { FeatureAccessLevel, FeatureCategory, LimitType } from '$lib/models/features/features';
import { syncPlanWithStripe } from './stripe';

/**
 * Get all plans from the database
 * @returns Array of PlanTier objects
 */
export async function getPlansFromDatabase(): Promise<PlanTier[]> {
  try {
    // Get all plans with their features and limits in a single query
    const dbPlans = await prisma.plan.findMany({
      orderBy: {
        monthlyPrice: 'asc',
      },
      include: {
        features: {
          include: {
            limits: true,
          },
        },
      },
    });

    // If no plans in database, initialize with default plans
    if (dbPlans.length === 0) {
      console.log('No plans found in the database. Initializing with default plans...');
      await initializePlansInDatabase();

      // Get plans again after initialization
      return getPlansFromDatabase();
    }

    return dbPlans.map(dbPlanToPlanTier);
  } catch (error) {
    console.error('Error getting plans from database:', error);
    return [];
  }
}

/**
 * Get a plan by ID from the database
 * @param planId The plan ID
 * @returns The PlanTier object or null if not found
 */
export async function getPlanById(planId: string): Promise<PlanTier | null> {
  try {
    // Get the plan with features and limits in a single query
    const dbPlan = await prisma.plan.findUnique({
      where: { id: planId },
      include: {
        features: {
          include: {
            limits: true,
          },
        },
      },
    });

    if (!dbPlan) {
      return null;
    }

    return dbPlanToPlanTier(dbPlan);
  } catch (error) {
    console.error(`Error getting plan ${planId}:`, error);
    return null;
  }
}

/**
 * Convert a database plan to a PlanTier object
 * @param dbPlan The database plan
 * @returns The PlanTier object
 */
export function dbPlanToPlanTier(dbPlan: any): PlanTier {
  return {
    id: dbPlan.id,
    name: dbPlan.name,
    description: dbPlan.description,
    section: dbPlan.section,
    monthlyPrice: dbPlan.monthlyPrice,
    annualPrice: dbPlan.annualPrice,
    stripePriceMonthlyId: dbPlan.stripePriceMonthlyId,
    stripePriceYearlyId: dbPlan.stripePriceYearlyId,
    popular: dbPlan.popular,
    features: dbPlan.features.map((feature: any) => ({
      featureId: feature.featureId,
      accessLevel: feature.accessLevel,
      limits: feature.limits?.map((limit: any) => ({
        limitId: limit.limitId,
        value: limit.value === 'unlimited' ? 'unlimited' : parseInt(limit.value, 10),
      })),
    })),
  };
}

/**
 * Initialize the database with plans if they don't exist
 * @returns The number of plans initialized
 */
export async function initializePlansInDatabase(): Promise<number> {
  try {
    // Check if any plans exist in the database
    const existingPlansCount = await prisma.plan.count();

    if (existingPlansCount > 0) {
      console.log(
        `${existingPlansCount} plans already exist in the database. Skipping initialization.`
      );
      return 0;
    }

    // No plans exist, so initialize with default plans
    console.log('No plans found in the database. Initializing with default plans...');

    // Create default features
    const defaultFeatures = [
      {
        id: 'dashboard',
        name: 'Dashboard',
        description: 'Access to the main dashboard',
        category: FeatureCategory.Core,
        icon: 'dashboard',
      },
      {
        id: 'profile',
        name: 'User Profile',
        description: 'User profile management',
        category: FeatureCategory.Core,
        icon: 'user',
      },
      {
        id: 'resume_scanner',
        name: 'Resume Scanner',
        description: 'Scan and analyze resumes',
        category: FeatureCategory.Resume,
        icon: 'scanner',
        limits: [
          {
            id: 'resume_scans_per_month',
            name: 'Resume Scans per Month',
            description: 'Maximum number of resume scans per month',
            defaultValue: 5,
            type: LimitType.Monthly,
            unit: 'scans',
            resetDay: 1,
          },
        ],
      },
      {
        id: 'resume_builder',
        name: 'Resume Builder',
        description: 'Create and edit resumes',
        category: FeatureCategory.Resume,
        icon: 'file-edit',
        limits: [
          {
            id: 'resume_versions',
            name: 'Resume Versions',
            description: 'Maximum number of resume versions',
            defaultValue: 1,
            type: LimitType.Total,
            unit: 'versions',
          },
          {
            id: 'resume_templates',
            name: 'Resume Templates',
            description: 'Available resume templates',
            defaultValue: 5,
            type: LimitType.Total,
            unit: 'templates',
          },
        ],
      },
      {
        id: 'resume_ai',
        name: 'Resume AI',
        description: 'AI-powered resume improvements',
        category: FeatureCategory.Advanced,
        icon: 'sparkles',
      },
      {
        id: 'job_search_profiles',
        name: 'Job Search Profiles',
        description: 'Create job search profiles',
        category: FeatureCategory.JobSearch,
        icon: 'search',
        limits: [
          {
            id: 'job_search_profiles_limit',
            name: 'Job Search Profiles Limit',
            description: 'Maximum number of job search profiles',
            defaultValue: 1,
            type: LimitType.Total,
            unit: 'profiles',
          },
        ],
      },
      {
        id: 'application_tracker',
        name: 'Application Tracker',
        description: 'Track job applications',
        category: FeatureCategory.Applications,
        icon: 'list-check',
      },
    ];

    // Sync all default features
    for (const feature of defaultFeatures) {
      await syncFeature(feature);
    }

    // Then create default plans
    let count = 0;

    // Create a free plan
    const freePlan: PlanTier = {
      id: 'free',
      name: 'Free',
      description: 'Basic features for personal use',
      section: 'pro',
      monthlyPrice: 0,
      annualPrice: 0,
      features: [
        // Add some basic features
        { featureId: 'dashboard', accessLevel: FeatureAccessLevel.Included },
        { featureId: 'profile', accessLevel: FeatureAccessLevel.Included },
        {
          featureId: 'resume_scanner',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'resume_scans_per_month', value: 5 }],
        },
        {
          featureId: 'resume_builder',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [
            { limitId: 'resume_versions', value: 1 },
            { limitId: 'resume_templates', value: 5 },
          ],
        },
        { featureId: 'resume_ai', accessLevel: FeatureAccessLevel.NotIncluded },
        {
          featureId: 'ats_optimization',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'ats_scans_monthly', value: 2 }],
        },
      ],
    };

    await syncPlan(freePlan);
    count++;

    // Create a basic plan
    const basicPlan: PlanTier = {
      id: 'basic',
      name: 'Basic',
      description: 'Essential features for individuals',
      section: 'pro',
      monthlyPrice: 999,
      annualPrice: 9990,
      features: [
        { featureId: 'dashboard', accessLevel: FeatureAccessLevel.Included },
        { featureId: 'profile', accessLevel: FeatureAccessLevel.Included },
        {
          featureId: 'resume_scanner',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'resume_scans_per_month', value: 20 }],
        },
        {
          featureId: 'resume_builder',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [
            { limitId: 'resume_versions', value: 3 },
            { limitId: 'resume_templates', value: 10 },
          ],
        },
        { featureId: 'resume_ai', accessLevel: FeatureAccessLevel.NotIncluded },
        {
          featureId: 'ats_optimization',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'ats_scans_monthly', value: 5 }],
        },
        {
          featureId: 'job_search_profiles',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'job_search_profiles_limit', value: 3 }],
        },
        { featureId: 'application_tracker', accessLevel: FeatureAccessLevel.Included },
      ],
    };

    await syncPlan(basicPlan);
    count++;

    // Create a pro plan
    const proPlan: PlanTier = {
      id: 'pro',
      name: 'Professional',
      description: 'Advanced features for professionals',
      section: 'pro',
      monthlyPrice: 1999,
      annualPrice: 19990,
      popular: true,
      features: [
        { featureId: 'dashboard', accessLevel: FeatureAccessLevel.Included },
        { featureId: 'profile', accessLevel: FeatureAccessLevel.Included },
        {
          featureId: 'resume_scanner',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'resume_scans_per_month', value: 50 }],
        },
        {
          featureId: 'resume_builder',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [
            { limitId: 'resume_versions', value: 10 },
            { limitId: 'resume_templates', value: 20 },
          ],
        },
        { featureId: 'resume_ai', accessLevel: FeatureAccessLevel.Included },
        {
          featureId: 'ats_optimization',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'ats_scans_monthly', value: 20 }],
        },
        {
          featureId: 'job_search_profiles',
          accessLevel: FeatureAccessLevel.Limited,
          limits: [{ limitId: 'job_search_profiles_limit', value: 5 }],
        },
        { featureId: 'application_tracker', accessLevel: FeatureAccessLevel.Included },
      ],
    };

    await syncPlan(proPlan);
    count++;

    console.log(`Successfully initialized ${count} default plans in the database.`);
    return count;
  } catch (error) {
    console.error('Error initializing plans in database:', error);
    throw error;
  }
}

/**
 * Sync a plan to the database
 * @param plan The plan to sync
 * @returns The synced plan
 */
export async function syncPlan(plan: PlanTier): Promise<any> {
  // Check if the plan already exists
  const existingPlan = await prisma.plan.findUnique({
    where: { id: plan.id },
  });

  if (existingPlan) {
    // Update the existing plan
    const updatedPlan = await prisma.plan.update({
      where: { id: plan.id },
      data: {
        name: plan.name,
        description: plan.description,
        section: plan.section,
        monthlyPrice: plan.monthlyPrice,
        annualPrice: plan.annualPrice,
        stripePriceMonthlyId: plan.stripePriceMonthlyId,
        stripePriceYearlyId: plan.stripePriceYearlyId,
        popular: plan.popular || false,
      },
    });

    // Sync the plan features
    for (const feature of plan.features) {
      await syncPlanFeature(feature, plan.id);
    }

    return updatedPlan;
  } else {
    // Create a new plan
    const newPlan = await prisma.plan.create({
      data: {
        id: plan.id,
        name: plan.name,
        description: plan.description,
        section: plan.section,
        monthlyPrice: plan.monthlyPrice,
        annualPrice: plan.annualPrice,
        stripePriceMonthlyId: plan.stripePriceMonthlyId,
        stripePriceYearlyId: plan.stripePriceYearlyId,
        popular: plan.popular || false,
        updatedAt: new Date(),
      },
    });

    // Sync the plan features
    for (const feature of plan.features) {
      await syncPlanFeature(feature, plan.id);
    }

    return newPlan;
  }
}

/**
 * Sync a feature to the database
 * @param feature The feature to sync
 * @returns The synced feature
 */
async function syncFeature(feature: Feature): Promise<any> {
  // Check if the feature already exists
  const existingFeature = await prisma.feature.findUnique({
    where: { id: feature.id },
  });

  if (existingFeature) {
    // Update the existing feature
    return await prisma.feature.update({
      where: { id: feature.id },
      data: {
        name: feature.name,
        description: feature.description,
        category: feature.category,
        icon: feature.icon,
        beta: feature.beta || false,
        updatedAt: new Date(),
      },
    });
  } else {
    // Create a new feature
    const newFeature = await prisma.feature.create({
      data: {
        id: feature.id,
        name: feature.name,
        description: feature.description,
        category: feature.category,
        icon: feature.icon,
        beta: feature.beta || false,
        updatedAt: new Date(),
      },
    });

    // Sync the feature limits
    if (feature.limits) {
      for (const limit of feature.limits) {
        await syncFeatureLimit(limit, newFeature.id);
      }
    }

    return newFeature;
  }
}

/**
 * Sync a plan feature to the database
 * @param feature The feature to sync
 * @param planId The plan ID
 * @returns The synced plan feature
 */
async function syncPlanFeature(feature: any, planId: string): Promise<any> {
  // Check if the plan feature already exists
  const existingFeature = await prisma.planFeature.findFirst({
    where: {
      planId: planId,
      featureId: feature.featureId,
    },
  });

  if (existingFeature) {
    // Update the existing plan feature
    const updatedFeature = await prisma.planFeature.update({
      where: { id: existingFeature.id },
      data: {
        accessLevel: feature.accessLevel,
      },
    });

    // Sync the feature limits
    if (feature.limits) {
      for (const limit of feature.limits) {
        await syncPlanFeatureLimit(limit, updatedFeature.id);
      }
    }

    return updatedFeature;
  } else {
    // Create a new plan feature
    const newFeature = await prisma.planFeature.create({
      data: {
        id: `${planId}_${feature.featureId}`,
        planId: planId,
        featureId: feature.featureId,
        accessLevel: feature.accessLevel,
        updatedAt: new Date(),
      },
    });

    // Sync the feature limits
    if (feature.limits) {
      for (const limit of feature.limits) {
        await syncPlanFeatureLimit(limit, newFeature.id);
      }
    }

    return newFeature;
  }
}

/**
 * Sync a feature limit to the database
 * @param limit The limit to sync
 * @param featureId The feature ID
 * @returns The synced feature limit
 */
async function syncFeatureLimit(limit: FeatureLimit, featureId: string): Promise<any> {
  // Check if the limit already exists
  const existingLimit = await prisma.featureLimit.findUnique({
    where: { id: limit.id },
  });

  if (existingLimit) {
    // Update the existing limit
    return await prisma.featureLimit.update({
      where: { id: limit.id },
      data: {
        name: limit.name,
        description: limit.description,
        defaultValue: limit.defaultValue.toString(),
        type: limit.type,
        unit: limit.unit,
        resetDay: limit.resetDay,
      },
    });
  } else {
    // Create a new limit
    return await prisma.featureLimit.create({
      data: {
        id: limit.id,
        featureId: featureId,
        name: limit.name,
        description: limit.description,
        defaultValue: limit.defaultValue.toString(),
        type: limit.type,
        unit: limit.unit,
        resetDay: limit.resetDay,
        updatedAt: new Date(),
      },
    });
  }
}

/**
 * Sync a plan feature limit to the database
 * @param limit The limit to sync
 * @param planFeatureId The plan feature ID
 * @returns The synced plan feature limit
 */
async function syncPlanFeatureLimit(limit: any, planFeatureId: string): Promise<any> {
  // Check if the limit already exists
  const existingLimit = await prisma.planFeatureLimit.findFirst({
    where: {
      planFeatureId: planFeatureId,
      limitId: limit.limitId,
    },
  });

  if (existingLimit) {
    // Update the existing limit
    return await prisma.planFeatureLimit.update({
      where: { id: existingLimit.id },
      data: {
        value: limit.value.toString(),
      },
    });
  } else {
    // Create a new limit
    return await prisma.planFeatureLimit.create({
      data: {
        id: `${planFeatureId}_${limit.limitId}`,
        planFeatureId: planFeatureId,
        limitId: limit.limitId,
        value: limit.value.toString(),
        updatedAt: new Date(),
      },
    });
  }
}

/**
 * Sync a plan with Stripe and update the database
 * @param planId The plan ID
 * @returns The updated plan
 */
export async function syncPlanWithStripeAndUpdateDb(planId: string): Promise<PlanTier | null> {
  const plan = await getPlanById(planId);

  if (!plan) return null;

  // Only sync paid plans
  if (plan.monthlyPrice === 0 && plan.annualPrice === 0) {
    return plan;
  }

  try {
    const updatedPlan = await syncPlanWithStripe(plan);

    // Update the plan in the database
    await prisma.plan.update({
      where: { id: planId },
      data: {
        stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,
        stripePriceYearlyId: updatedPlan.stripePriceYearlyId,
      },
    });

    return updatedPlan;
  } catch (error) {
    console.error(`Error syncing plan ${planId} with Stripe:`, error);
    return plan;
  }
}
