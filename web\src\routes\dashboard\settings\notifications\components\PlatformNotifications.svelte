<script lang="ts">
  import * as Switch from '$lib/components/ui/switch/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import type { SuperForm } from 'sveltekit-superforms';
  import type { z } from 'zod';
  import type { notificationFormSchema } from '$lib/schemas/notification-schema';
  import {
    requestPushNotificationPermission,
    unregisterPushNotifications,
    getPushNotificationStatus,
  } from '$lib/push-notifications';
  import { onMount } from 'svelte';
  import { toast } from 'svelte-sonner';

  export let formData: SuperForm<z.infer<typeof notificationFormSchema>>;

  // Extract the form data store from the SuperForm
  const { form } = formData;

  // State for push notification handling
  let isLoading = false;
  let pushStatus = {
    supported: false,
    permission: 'default' as NotificationPermission,
    hasSubscription: false,
    serviceWorkerRegistered: false,
  };

  // Function to trigger form change event
  function triggerFormChange() {
    // Dispatch a change event to the parent form
    const formElement = document.getElementById('notification-form');
    if (formElement) {
      formElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // Check push notification status on mount
  onMount(async () => {
    pushStatus = await getPushNotificationStatus();

    // Sync form state with actual browser state
    if (pushStatus.permission === 'granted' && pushStatus.hasSubscription) {
      if (!$form.pushNotifications) {
        form.update((f) => ({ ...f, pushNotifications: true }));
      }
    } else {
      if ($form.pushNotifications) {
        form.update((f) => ({ ...f, pushNotifications: false }));
      }
    }
  });

  // Handle push notification toggle
  async function handlePushNotificationToggle(checked: boolean) {
    if (isLoading) return;

    isLoading = true;

    try {
      if (checked) {
        // Enable push notifications
        const result = await requestPushNotificationPermission();

        if (result.success) {
          form.update((f) => ({ ...f, pushNotifications: true }));
          triggerFormChange();
          toast.success('Push notifications enabled successfully!');

          // Update status
          pushStatus = await getPushNotificationStatus();
        } else {
          // Reset the switch if enabling failed
          form.update((f) => ({ ...f, pushNotifications: false }));
          toast.error(result.error || 'Failed to enable push notifications');
        }
      } else {
        // Disable push notifications
        const result = await unregisterPushNotifications();

        if (result.success) {
          form.update((f) => ({ ...f, pushNotifications: false }));
          triggerFormChange();
          toast.success('Push notifications disabled successfully');

          // Update status
          pushStatus = await getPushNotificationStatus();
        } else {
          // Reset the switch if disabling failed
          form.update((f) => ({ ...f, pushNotifications: true }));
          toast.error(result.error || 'Failed to disable push notifications');
        }
      }
    } catch (error) {
      console.error('Error handling push notification toggle:', error);
      toast.error('An unexpected error occurred');

      // Reset switch to previous state
      form.update((f) => ({ ...f, pushNotifications: !checked }));
    } finally {
      isLoading = false;
    }
  }

  // Test push notification function
  async function testPushNotification() {
    try {
      const response = await fetch('/api/push/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Test push notification sent! Check your browser notifications.');
      } else {
        toast.error(result.message || 'Failed to send test notification');
      }
    } catch (error) {
      console.error('Error testing push notification:', error);
      toast.error('Error testing push notification. Please try again.');
    }
  }
</script>

<!-- Platform Settings -->
<div class="space-y-6">
  <!-- Browser Notifications -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <div class="font-medium">Browser Notifications</div>
      <Switch.Root
        checked={Boolean($form.browserNotifications)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, browserNotifications: checked }));
          triggerFormChange();
        }} />
    </div>
    <div class="text-muted-foreground text-sm">
      Receive notifications in your browser when you're on the site
    </div>
  </div>

  <!-- Push Notifications -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Push Notifications</div>
        <div class="text-muted-foreground text-sm">
          Receive push notifications for important updates
        </div>
        {#if !pushStatus.supported}
          <div class="text-destructive text-xs">
            Push notifications are not supported in this browser
          </div>
        {:else if pushStatus.permission === 'denied'}
          <div class="text-destructive text-xs">
            Push notifications are blocked. Please enable them in your browser settings.
          </div>
        {/if}
      </div>
      <Switch.Root
        checked={Boolean($form.pushNotifications)}
        disabled={isLoading || !pushStatus.supported || pushStatus.permission === 'denied'}
        onCheckedChange={handlePushNotificationToggle} />
    </div>

    {#if Boolean($form.pushNotifications) && pushStatus.hasSubscription}
      <div class="flex justify-start">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onclick={testPushNotification}
          disabled={isLoading}>
          Test Push Notification
        </Button>
      </div>
    {/if}

    {#if isLoading}
      <div class="text-muted-foreground text-xs">Processing push notification settings...</div>
    {/if}
  </div>
</div>
