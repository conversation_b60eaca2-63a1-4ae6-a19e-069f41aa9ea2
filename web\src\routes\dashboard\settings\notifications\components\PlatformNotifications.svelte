<script lang="ts">
  import * as Switch from '$lib/components/ui/switch/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import type { SuperForm } from 'sveltekit-superforms';
  import type { z } from 'zod';
  import type { notificationFormSchema } from '$lib/schemas/notification-schema';
  import {
    requestPushNotificationPermission,
    unregisterPushNotifications,
    getPushNotificationStatus,
    testRequestPermission,
    resetPushNotifications,
    forceRequestPermission,
  } from '$lib/push-notifications';
  import { onMount } from 'svelte';
  import { toast } from 'svelte-sonner';

  export let formData: SuperForm<z.infer<typeof notificationFormSchema>>;

  // Extract the form data store from the SuperForm
  const { form } = formData;

  // State for push notification handling
  let isLoading = false;
  let pushStatus = {
    supported: false,
    permission: 'default' as NotificationPermission,
    hasSubscription: false,
    serviceWorkerRegistered: false,
  };

  // Function to trigger form change event
  function triggerFormChange() {
    // Dispatch a change event to the parent form
    const formElement = document.getElementById('notification-form');
    if (formElement) {
      formElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // Check push notification status on mount
  onMount(async () => {
    pushStatus = await getPushNotificationStatus();

    // Sync form state with actual browser state
    if (pushStatus.permission === 'granted' && pushStatus.hasSubscription) {
      if (!$form.pushNotifications) {
        form.update((f) => ({ ...f, pushNotifications: true }));
      }
    } else {
      if ($form.pushNotifications) {
        form.update((f) => ({ ...f, pushNotifications: false }));
      }
    }
  });

  // Handle push notification toggle
  async function handlePushNotificationToggle(checked: boolean) {
    console.log('🔄 Push notification toggle clicked:', checked);

    if (isLoading) {
      console.log('⏳ Already loading, ignoring click');
      return;
    }

    isLoading = true;

    try {
      if (checked) {
        console.log('🔔 Enabling push notifications...');
        // Request permission first - don't turn on toggle until user accepts
        const result = await requestPushNotificationPermission();
        console.log('🔔 Permission request result:', result);

        if (result.success) {
          // Only update form and trigger save AFTER permission is granted and subscription is created
          console.log('✅ Permission granted, updating form...');
          form.update((f) => ({ ...f, pushNotifications: true }));
          triggerFormChange(); // This will trigger the form submission to save to DB
          toast.success('Push notifications enabled successfully!');

          // Update status
          pushStatus = await getPushNotificationStatus();
        } else {
          // Keep the switch off if permission was denied or failed
          console.log('❌ Permission failed, keeping switch off');
          form.update((f) => ({ ...f, pushNotifications: false }));
          toast.error(result.error || 'Failed to enable push notifications');
        }
      } else {
        console.log('🔕 Disabling push notifications...');
        // Disable push notifications
        const result = await unregisterPushNotifications();

        if (result.success) {
          // Update form and trigger save to DB
          form.update((f) => ({ ...f, pushNotifications: false }));
          triggerFormChange(); // This will trigger the form submission to save to DB
          toast.success('Push notifications disabled successfully');

          // Update status
          pushStatus = await getPushNotificationStatus();
        } else {
          // Reset the switch if disabling failed
          form.update((f) => ({ ...f, pushNotifications: true }));
          toast.error(result.error || 'Failed to disable push notifications');
        }
      }
    } catch (error) {
      console.error('❌ Error handling push notification toggle:', error);
      toast.error('An unexpected error occurred');

      // Reset switch to previous state
      form.update((f) => ({ ...f, pushNotifications: !checked }));
    } finally {
      isLoading = false;
    }
  }

  // Test permission request function (for debugging)
  async function testPermissionRequest() {
    try {
      const permission = await testRequestPermission();
      toast.success(`Permission result: ${permission}`);

      // Update status after permission test
      pushStatus = await getPushNotificationStatus();
    } catch (error) {
      console.error('Error testing permission request:', error);
      toast.error('Error testing permission request');
    }
  }

  // Force permission request (shows dialog even if already granted)
  async function forcePermissionRequest() {
    try {
      const permission = await forceRequestPermission();
      toast.success(`Permission result: ${permission}`);

      // Update status after permission test
      pushStatus = await getPushNotificationStatus();
    } catch (error) {
      console.error('Error forcing permission request:', error);
      toast.error('Error requesting permission');
    }
  }

  // Reset all push notification settings
  async function resetPushSettings() {
    try {
      const result = await resetPushNotifications();
      if (result.success) {
        toast.success('Push notifications reset successfully! You can now enable them again.');

        // Update form and status
        form.update((f) => ({ ...f, pushNotifications: false }));
        pushStatus = await getPushNotificationStatus();
        triggerFormChange();
      } else {
        toast.error(result.error || 'Failed to reset push notifications');
      }
    } catch (error) {
      console.error('Error resetting push notifications:', error);
      toast.error('Error resetting push notifications');
    }
  }

  // Test push notification function
  async function testPushNotification() {
    try {
      const response = await fetch('/api/push/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Test push notification sent! Check your browser notifications.');
      } else {
        toast.error(result.message || 'Failed to send test notification');
      }
    } catch (error) {
      console.error('Error testing push notification:', error);
      toast.error('Error testing push notification. Please try again.');
    }
  }
</script>

<!-- Platform Settings -->
<div class="space-y-6">
  <!-- Browser Notifications -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <div class="font-medium">Browser Notifications</div>
      <Switch.Root
        checked={Boolean($form.browserNotifications)}
        onCheckedChange={(checked) => {
          form.update((f) => ({ ...f, browserNotifications: checked }));
          triggerFormChange();
        }} />
    </div>
    <div class="text-muted-foreground text-sm">
      Receive notifications in your browser when you're on the site
    </div>
  </div>

  <!-- Push Notifications -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <div class="font-medium">Push Notifications</div>
        <div class="text-muted-foreground text-sm">
          Receive push notifications for important updates
        </div>

        <!-- Permission Status Display -->
        <div class="space-y-1 text-xs">
          <div class="flex items-center gap-2">
            <span class="font-medium">Status:</span>
            {#if !pushStatus.supported}
              <span class="text-destructive">❌ Not supported in this browser</span>
            {:else if pushStatus.permission === 'granted'}
              <span class="text-green-600">✅ Permission granted</span>
            {:else if pushStatus.permission === 'denied'}
              <span class="text-destructive">❌ Permission blocked</span>
            {:else}
              <span class="text-yellow-600">⚠️ Permission not requested</span>
            {/if}
          </div>

          {#if pushStatus.supported}
            <div class="flex items-center gap-2">
              <span class="font-medium">Subscription:</span>
              {#if pushStatus.hasSubscription}
                <span class="text-green-600">✅ Active</span>
              {:else}
                <span class="text-muted-foreground">❌ None</span>
              {/if}
            </div>
          {/if}
        </div>

        <!-- Help text for blocked permissions -->
        {#if pushStatus.permission === 'denied'}
          <div class="text-destructive bg-destructive/10 rounded p-2 text-xs">
            <strong>Notifications are blocked.</strong> To enable:
            <br />1. Click the lock icon (🔒) in your address bar
            <br />2. Change "Notifications" to "Allow"
            <br />3. Refresh the page and try again
          </div>
        {/if}
      </div>
      <Switch.Root
        checked={Boolean($form.pushNotifications) &&
          pushStatus.permission === 'granted' &&
          pushStatus.hasSubscription}
        disabled={isLoading || !pushStatus.supported || pushStatus.permission === 'denied'}
        onCheckedChange={handlePushNotificationToggle} />
    </div>

    {#if Boolean($form.pushNotifications) && pushStatus.hasSubscription}
      <div class="flex justify-start gap-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onclick={testPushNotification}
          disabled={isLoading}>
          Test Push Notification
        </Button>
      </div>
    {/if}

    <!-- Debug and management buttons -->
    <div class="flex flex-wrap gap-2">
      <Button
        type="button"
        variant="secondary"
        size="sm"
        onclick={testPermissionRequest}
        disabled={isLoading}>
        🔧 Test Permission
      </Button>

      <Button
        type="button"
        variant="secondary"
        size="sm"
        onclick={forcePermissionRequest}
        disabled={isLoading}>
        🔔 Force Permission Dialog
      </Button>

      {#if pushStatus.hasSubscription || pushStatus.permission === 'granted'}
        <Button
          type="button"
          variant="destructive"
          size="sm"
          onclick={resetPushSettings}
          disabled={isLoading}>
          🔄 Reset & Clear All
        </Button>
      {/if}
    </div>

    {#if isLoading}
      <div class="text-muted-foreground text-xs">Processing push notification settings...</div>
    {/if}
  </div>
</div>
