<script lang="ts">
  import * as Switch from '$lib/components/ui/switch/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import type { SuperForm } from 'sveltekit-superforms';
  import type { z } from 'zod';
  import type { notificationFormSchema } from '$lib/schemas/notification-schema';

  export let formData: SuperForm<z.infer<typeof notificationFormSchema>>;

  // Extract the form data store from the SuperForm
  const { form } = formData;

  // Function to trigger form change event
  function triggerFormChange() {
    // Dispatch a change event to the parent form
    const formElement = document.getElementById('notification-form');
    if (formElement) {
      formElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // Using direct form access with SuperForms
</script>

<div class="space-y-6">
  <!-- Email Notifications Switch -->
  <div class="flex items-center justify-between">
    <div class="space-y-0.5">
      <div class="font-medium">Email Notifications</div>
      <div class="text-muted-foreground text-sm">Receive notifications via email</div>
    </div>
    <Switch.Root
      checked={Boolean($form.emailNotifications)}
      onCheckedChange={(checked) => {
        form.update((f) => ({ ...f, emailNotifications: checked }));
        triggerFormChange();
      }} />
  </div>

  {#if Boolean($form.emailNotifications)}
    <!-- Email Digest Frequency -->
    <div class="space-y-2">
      <div class="font-medium">Email Digest Frequency</div>
      <Select.Root
        type="single"
        value={$form.emailDigest || 'daily'}
        onValueChange={(value: 'daily' | 'weekly' | 'never') => {
          form.update((f) => ({
            ...f,
            emailDigest: value,
          }));
          triggerFormChange();
        }}>
        <Select.Trigger class="w-full">
          {$form.emailDigest === 'daily'
            ? 'Daily'
            : $form.emailDigest === 'weekly'
              ? 'Weekly'
              : $form.emailDigest === 'never'
                ? 'Never'
                : 'Select frequency'}
        </Select.Trigger>
        <Select.Content class="max-h-60">
          <Select.Item value="daily">Daily</Select.Item>
          <Select.Item value="weekly">Weekly</Select.Item>
          <Select.Item value="never">Never</Select.Item>
        </Select.Content>
      </Select.Root>
      <div class="text-muted-foreground text-sm">
        How often you want to receive email digests summarizing your notifications
      </div>
    </div>

    <!-- Email Format -->
    <div class="space-y-2">
      <div class="font-medium">Email Format</div>
      <Select.Root
        type="single"
        value={$form.emailFormat || 'html'}
        onValueChange={(value: 'html' | 'text') => {
          form.update((f) => ({
            ...f,
            emailFormat: value,
          }));
          triggerFormChange();
        }}>
        <Select.Trigger class="w-full">
          {$form.emailFormat === 'html'
            ? 'HTML (Rich formatting)'
            : $form.emailFormat === 'text'
              ? 'Plain Text'
              : 'Select format'}
        </Select.Trigger>
        <Select.Content class="max-h-60">
          <Select.Item value="html">HTML (Rich formatting)</Select.Item>
          <Select.Item value="text">Plain Text</Select.Item>
        </Select.Content>
      </Select.Root>
      <div class="text-muted-foreground text-sm">
        Choose how you want your emails to be formatted
      </div>
    </div>
  {/if}
</div>
