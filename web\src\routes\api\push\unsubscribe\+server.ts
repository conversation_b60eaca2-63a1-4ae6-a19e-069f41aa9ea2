import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

/**
 * Unsubscribe user from push notifications
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    // Authenticate the user
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the push subscription data
    const subscription = await request.json();

    if (subscription?.endpoint) {
      // Remove specific subscription
      await prisma.pushSubscription.deleteMany({
        where: {
          userId: user.id,
          endpoint: subscription.endpoint,
        },
      });
    } else {
      // Remove all subscriptions for the user
      await prisma.pushSubscription.deleteMany({
        where: {
          userId: user.id,
        },
      });
    }

    // Update user's push notification preference
    await prisma.notificationSettings.upsert({
      where: { userId: user.id },
      update: { pushEnabled: false },
      create: {
        userId: user.id,
        pushEnabled: false,
        emailEnabled: true,
        browserEnabled: true,
        jobMatchEnabled: true,
        applicationStatusEnabled: true,
        automationEnabled: true,
      },
    });

    console.log(`Push subscription removed for user ${user.id}`);
    return json({ success: true });
  } catch (error) {
    console.error('Error removing push subscription:', error);
    return json({ error: 'Failed to remove subscription' }, { status: 500 });
  }
};
