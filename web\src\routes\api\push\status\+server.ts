import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

/**
 * Get push notification status for the authenticated user
 */
export const GET: RequestHandler = async ({ cookies }) => {
  try {
    // Authenticate the user
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's notification settings
    const notificationSettings = await prisma.notificationSettings.findUnique({
      where: { userId: user.id },
    });

    // Get user's push subscriptions
    const subscriptions = await prisma.pushSubscription.findMany({
      where: { userId: user.id },
    });

    return json({
      pushEnabled: notificationSettings?.pushEnabled ?? false,
      hasSubscriptions: subscriptions.length > 0,
      subscriptionCount: subscriptions.length,
      lastUpdated: notificationSettings?.updatedAt?.toISOString(),
    });
  } catch (error) {
    console.error('Error getting push notification status:', error);
    return json({ error: 'Failed to get push notification status' }, { status: 500 });
  }
};
