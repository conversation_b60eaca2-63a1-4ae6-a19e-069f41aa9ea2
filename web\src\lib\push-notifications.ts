import { browser } from '$app/environment';

// VAPID public key - this should match the server-side key
const VAPID_PUBLIC_KEY = 'BEl62iUYgUivxIkv69yViEuiBIa40HdHSWgNfZeeyCLdmw';

/**
 * Check if push notifications are supported
 */
export function isPushNotificationSupported(): boolean {
  if (!browser) return false;
  return 'serviceWorker' in navigator && 'PushManager' in window;
}

/**
 * Check current notification permission status
 */
export function getNotificationPermission(): NotificationPermission {
  if (!browser || !('Notification' in window)) return 'default';
  return Notification.permission;
}

/**
 * Get current push notification status
 */
export async function getPushNotificationStatus(): Promise<{
  supported: boolean;
  permission: NotificationPermission;
  hasSubscription: boolean;
  serviceWorkerRegistered: boolean;
}> {
  const supported = isPushNotificationSupported();
  const permission = getNotificationPermission();

  let hasSubscription = false;
  let serviceWorkerRegistered = false;

  if (supported) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      serviceWorkerRegistered = registration !== null;

      if (registration) {
        const subscription = await registration.pushManager.getSubscription();
        hasSubscription = subscription !== null;
      }
    } catch (error) {
      console.error('Error getting push notification status:', error);
    }
  }

  return {
    supported,
    permission,
    hasSubscription,
    serviceWorkerRegistered,
  };
}

/**
 * Request permission for push notifications and register service worker
 */
export async function requestPushNotificationPermission(): Promise<{
  success: boolean;
  error?: string;
  permission?: NotificationPermission;
}> {
  if (!isPushNotificationSupported()) {
    return {
      success: false,
      error: 'Push notifications are not supported in this browser',
    };
  }

  try {
    // Check current permission
    let permission = getNotificationPermission();

    // Request permission if not already granted
    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }

    if (permission !== 'granted') {
      return {
        success: false,
        error:
          permission === 'denied'
            ? 'Push notifications are blocked. Please enable them in your browser settings.'
            : 'Push notification permission was not granted',
        permission,
      };
    }

    // Register service worker
    let registration;
    try {
      registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service worker registered:', registration);

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;
    } catch (swError) {
      console.error('Service worker registration failed:', swError);
      return {
        success: false,
        error: 'Failed to register service worker for push notifications',
        permission,
      };
    }

    // Check if already subscribed
    const existingSubscription = await registration.pushManager.getSubscription();
    if (existingSubscription) {
      // Verify subscription with server
      try {
        const response = await fetch('/api/push/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(existingSubscription),
        });

        if (response.ok) {
          console.log('Existing push subscription verified');
          return { success: true, permission };
        }
      } catch (error) {
        console.warn('Failed to verify existing subscription, creating new one');
      }
    }

    // Subscribe to push notifications
    let subscription;
    try {
      subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY),
      });
    } catch (subscribeError) {
      console.error('Failed to subscribe to push notifications:', subscribeError);
      return {
        success: false,
        error: 'Failed to create push notification subscription',
        permission,
      };
    }

    // Send subscription to server
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to save subscription to server');
      }

      console.log('Push notifications enabled successfully');
      return { success: true, permission };
    } catch (serverError) {
      console.error('Failed to save subscription to server:', serverError);

      // Clean up the subscription if server save failed
      try {
        await subscription.unsubscribe();
      } catch (cleanupError) {
        console.error('Failed to cleanup subscription:', cleanupError);
      }

      return {
        success: false,
        error: 'Failed to save push notification settings. Please try again.',
        permission,
      };
    }
  } catch (error) {
    console.error('Error enabling push notifications:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while enabling push notifications',
    };
  }
}

/**
 * Unregister push notifications
 */
export async function unregisterPushNotifications(): Promise<{
  success: boolean;
  error?: string;
}> {
  if (!isPushNotificationSupported()) {
    return {
      success: false,
      error: 'Push notifications are not supported in this browser',
    };
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) {
      console.log('No service worker registration found');
      return { success: true }; // Consider it successful if nothing to unregister
    }

    const subscription = await registration.pushManager.getSubscription();
    if (!subscription) {
      console.log('No push subscription found');
      return { success: true }; // Consider it successful if nothing to unregister
    }

    // Unsubscribe from push notifications
    try {
      await subscription.unsubscribe();
      console.log('Push subscription unsubscribed');
    } catch (unsubscribeError) {
      console.error('Failed to unsubscribe from push notifications:', unsubscribeError);
      // Continue to notify server even if local unsubscribe failed
    }

    // Notify server to remove subscription
    try {
      const response = await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to remove subscription from server:', errorData);
        return {
          success: false,
          error: 'Failed to remove push notification settings from server',
        };
      }
    } catch (serverError) {
      console.error('Error notifying server about unsubscription:', serverError);
      return {
        success: false,
        error: 'Failed to update push notification settings on server',
      };
    }

    console.log('Push notifications unregistered successfully');
    return { success: true };
  } catch (error) {
    console.error('Error unregistering push notifications:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while disabling push notifications',
    };
  }
}

/**
 * Check if user is currently subscribed to push notifications
 */
export async function isPushNotificationSubscribed(): Promise<boolean> {
  if (!isPushNotificationSupported()) return false;

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) return false;

    const subscription = await registration.pushManager.getSubscription();
    return subscription !== null;
  } catch (error) {
    console.error('Error checking push subscription status:', error);
    return false;
  }
}

/**
 * Convert VAPID key from base64 to Uint8Array
 */
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Show a test notification (for testing purposes)
 */
export async function showTestNotification(): Promise<void> {
  if (!isPushNotificationSupported()) return;

  if (getNotificationPermission() === 'granted') {
    new Notification('Test Notification', {
      body: 'This is a test notification from Auto Apply',
      icon: '/favicon.ico',
      badge: '/favicon.ico',
    });
  }
}
