import { browser } from '$app/environment';

// VAPID public key - this should match the server-side key
// This is a placeholder key - you need to generate proper VAPID keys for production
const VAPID_PUBLIC_KEY =
  'BEl62iUYgUivxIkv69yViEuiBIa40HdHSWgNfZeeyCLdmwHaFJfFETqd77VnQjZdAktiG8L8-fCqQjlPENcmWNi4';

/**
 * Check if push notifications are supported
 */
export function isPushNotificationSupported(): boolean {
  if (!browser) {
    console.log('❌ Not in browser environment');
    return false;
  }

  // Check HTTPS requirement
  if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
    console.log('❌ Push notifications require HTTPS (except on localhost)');
    return false;
  }

  const hasServiceWorker = 'serviceWorker' in navigator;
  const hasPushManager = 'PushManager' in window;
  const hasNotification = 'Notification' in window;

  console.log('🔍 Push notification support check:', {
    protocol: location.protocol,
    hostname: location.hostname,
    serviceWorker: hasServiceWorker,
    PushManager: hasPushManager,
    Notification: hasNotification,
    supported: hasServiceWorker && hasPushManager && hasNotification,
  });

  return hasServiceWorker && hasPushManager && hasNotification;
}

/**
 * Check current notification permission status
 */
export function getNotificationPermission(): NotificationPermission {
  if (!browser || !('Notification' in window)) return 'default';
  return Notification.permission;
}

/**
 * Get current push notification status
 */
export async function getPushNotificationStatus(): Promise<{
  supported: boolean;
  permission: NotificationPermission;
  hasSubscription: boolean;
  serviceWorkerRegistered: boolean;
}> {
  const supported = isPushNotificationSupported();
  const permission = getNotificationPermission();

  let hasSubscription = false;
  let serviceWorkerRegistered = false;

  if (supported) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      serviceWorkerRegistered = registration !== null;

      if (registration) {
        const subscription = await registration.pushManager.getSubscription();
        hasSubscription = subscription !== null;
      }
    } catch (error) {
      console.error('Error getting push notification status:', error);
    }
  }

  return {
    supported,
    permission,
    hasSubscription,
    serviceWorkerRegistered,
  };
}

/**
 * Request permission for push notifications and register service worker
 */
export async function requestPushNotificationPermission(): Promise<{
  success: boolean;
  error?: string;
  permission?: NotificationPermission;
}> {
  if (!isPushNotificationSupported()) {
    return {
      success: false,
      error: 'Push notifications are not supported in this browser',
    };
  }

  try {
    // Check current permission
    let permission = getNotificationPermission();
    console.log('🔔 Current notification permission:', permission);
    console.log('🔔 Browser support check:', {
      serviceWorker: 'serviceWorker' in navigator,
      PushManager: 'PushManager' in window,
      Notification: 'Notification' in window,
    });

    // Handle different permission states
    if (permission === 'denied') {
      console.log('❌ Permission is denied. User must manually enable in browser settings.');
      return {
        success: false,
        error:
          'Push notifications are blocked. Please click the lock icon in your address bar and allow notifications, then try again.',
        permission,
      };
    }

    // Only request permission if it's default (not yet asked)
    if (permission === 'default') {
      console.log('🔔 Permission is default, requesting permission...');
      permission = await Notification.requestPermission();
      console.log('🔔 Permission result after request:', permission);
    } else if (permission === 'granted') {
      console.log('✅ Permission already granted, proceeding with subscription...');
    }

    if (permission !== 'granted') {
      return {
        success: false,
        error:
          permission === 'denied'
            ? 'Push notifications are blocked. Please enable them in your browser settings.'
            : 'Push notification permission was not granted',
        permission,
      };
    }

    // Register service worker
    console.log('🔧 Registering service worker...');
    let registration: ServiceWorkerRegistration;
    try {
      registration = await navigator.serviceWorker.register('/sw.js');
      console.log('✅ Service worker registered:', registration);

      // Wait for service worker to be ready
      console.log('⏳ Waiting for service worker to be ready...');
      await navigator.serviceWorker.ready;
      console.log('✅ Service worker is ready');
    } catch (swError) {
      console.error('❌ Service worker registration failed:', swError);
      return {
        success: false,
        error: 'Failed to register service worker for push notifications',
        permission,
      };
    }

    // Check if already subscribed
    console.log('🔍 Checking for existing subscription...');
    const existingSubscription = await registration.pushManager.getSubscription();

    if (existingSubscription) {
      console.log('✅ Found existing subscription:', existingSubscription.endpoint);
      // Verify subscription with server
      try {
        console.log('🔄 Verifying subscription with server...');
        const response = await fetch('/api/push/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(existingSubscription),
        });

        if (response.ok) {
          console.log('✅ Existing push subscription verified with server');
          return { success: true, permission };
        } else {
          console.warn('⚠️ Server verification failed, will create new subscription');
        }
      } catch (error) {
        console.warn('⚠️ Failed to verify existing subscription, creating new one:', error);
      }
    } else {
      console.log('❌ No existing subscription found, will create new one');
    }

    // Subscribe to push notifications
    console.log('🔔 Creating new push subscription...');
    console.log('🔑 Using VAPID key:', VAPID_PUBLIC_KEY.substring(0, 20) + '...');

    let subscription: PushSubscription;
    try {
      subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY),
      });
      console.log('✅ Push subscription created successfully:', subscription.endpoint);
    } catch (subscribeError) {
      console.error('❌ Failed to subscribe to push notifications:', subscribeError);
      console.error('❌ Subscribe error details:', {
        name: subscribeError.name,
        message: subscribeError.message,
        stack: subscribeError.stack,
      });
      return {
        success: false,
        error: `Failed to create push notification subscription: ${subscribeError.message}`,
        permission,
      };
    }

    // Send subscription to server
    console.log('📤 Sending subscription to server...');
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      console.log('📥 Server response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Server error response:', errorData);
        throw new Error(errorData.error || 'Failed to save subscription to server');
      }

      const responseData = await response.json().catch(() => ({}));
      console.log('✅ Server response data:', responseData);
      console.log('🎉 Push notifications enabled successfully!');
      return { success: true, permission };
    } catch (serverError) {
      console.error('❌ Failed to save subscription to server:', serverError);

      // Clean up the subscription if server save failed
      try {
        console.log('🧹 Cleaning up failed subscription...');
        await subscription.unsubscribe();
        console.log('✅ Subscription cleanup completed');
      } catch (cleanupError) {
        console.error('❌ Failed to cleanup subscription:', cleanupError);
      }

      return {
        success: false,
        error: 'Failed to save push notification settings. Please try again.',
        permission,
      };
    }
  } catch (error) {
    console.error('Error enabling push notifications:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while enabling push notifications',
    };
  }
}

/**
 * Unregister push notifications
 */
export async function unregisterPushNotifications(): Promise<{
  success: boolean;
  error?: string;
}> {
  if (!isPushNotificationSupported()) {
    return {
      success: false,
      error: 'Push notifications are not supported in this browser',
    };
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) {
      console.log('No service worker registration found');
      return { success: true }; // Consider it successful if nothing to unregister
    }

    const subscription = await registration.pushManager.getSubscription();
    if (!subscription) {
      console.log('No push subscription found');
      return { success: true }; // Consider it successful if nothing to unregister
    }

    // Unsubscribe from push notifications
    try {
      await subscription.unsubscribe();
      console.log('Push subscription unsubscribed');
    } catch (unsubscribeError) {
      console.error('Failed to unsubscribe from push notifications:', unsubscribeError);
      // Continue to notify server even if local unsubscribe failed
    }

    // Notify server to remove subscription
    try {
      const response = await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to remove subscription from server:', errorData);
        return {
          success: false,
          error: 'Failed to remove push notification settings from server',
        };
      }
    } catch (serverError) {
      console.error('Error notifying server about unsubscription:', serverError);
      return {
        success: false,
        error: 'Failed to update push notification settings on server',
      };
    }

    console.log('Push notifications unregistered successfully');
    return { success: true };
  } catch (error) {
    console.error('Error unregistering push notifications:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while disabling push notifications',
    };
  }
}

/**
 * Check if user is currently subscribed to push notifications
 */
export async function isPushNotificationSubscribed(): Promise<boolean> {
  if (!isPushNotificationSupported()) return false;

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) return false;

    const subscription = await registration.pushManager.getSubscription();
    return subscription !== null;
  } catch (error) {
    console.error('Error checking push subscription status:', error);
    return false;
  }
}

/**
 * Convert VAPID key from base64 to Uint8Array
 */
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Reset push notification permissions and subscriptions
 * This will clear all subscriptions and allow the user to see the permission dialog again
 */
export async function resetPushNotifications(): Promise<{
  success: boolean;
  error?: string;
}> {
  console.log('🔄 Resetting push notifications...');

  try {
    // First, unregister all existing subscriptions
    const unregisterResult = await unregisterPushNotifications();
    if (!unregisterResult.success) {
      console.warn('⚠️ Failed to unregister existing subscriptions:', unregisterResult.error);
    }

    // Clear any cached service worker registrations
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        try {
          await registration.unregister();
          console.log('🗑️ Unregistered service worker:', registration.scope);
        } catch (error) {
          console.warn('⚠️ Failed to unregister service worker:', error);
        }
      }
    }

    console.log('✅ Push notifications reset completed');
    return { success: true };
  } catch (error) {
    console.error('❌ Error resetting push notifications:', error);
    return {
      success: false,
      error: 'Failed to reset push notifications',
    };
  }
}

/**
 * Force request permission dialog (even if already granted)
 * This is useful for testing or when user wants to re-enable after blocking
 */
export async function forceRequestPermission(): Promise<NotificationPermission> {
  console.log('🔔 Force requesting notification permission...');

  if (!browser || !('Notification' in window)) {
    console.error('❌ Notification API not supported');
    return 'default';
  }

  console.log('📋 Current permission:', Notification.permission);

  try {
    // Always request permission to show the dialog
    const permission = await Notification.requestPermission();
    console.log('✅ Permission request result:', permission);

    if (permission === 'granted') {
      console.log('🎉 Permission granted! Dialog should have appeared.');
    } else if (permission === 'denied') {
      console.log('❌ Permission denied. User blocked notifications.');
    } else {
      console.log('⚠️ Permission default. User dismissed without choosing.');
    }

    return permission;
  } catch (error) {
    console.error('❌ Error requesting permission:', error);
    return 'default';
  }
}

/**
 * Simple test to request notification permission only (for debugging)
 */
export async function testRequestPermission(): Promise<NotificationPermission> {
  console.log('🧪 Testing notification permission request...');

  if (!browser) {
    console.error('❌ Not in browser environment');
    return 'default';
  }

  if (!('Notification' in window)) {
    console.error('❌ Notification API not supported');
    return 'default';
  }

  console.log('✅ Browser environment and Notification API available');
  console.log('📋 Current permission:', Notification.permission);

  // Only request permission if it's default
  if (Notification.permission === 'default') {
    console.log('🔔 Calling Notification.requestPermission()...');

    try {
      const permission = await Notification.requestPermission();
      console.log('✅ Permission request completed. Result:', permission);

      if (permission === 'granted') {
        console.log('🎉 Permission granted! You should have seen a browser dialog.');
      } else if (permission === 'denied') {
        console.log('❌ Permission denied. User clicked "Block" or "Don\'t Allow".');
      } else {
        console.log('⚠️ Permission default. User dismissed dialog without choosing.');
      }

      return permission;
    } catch (error) {
      console.error('❌ Error requesting permission:', error);
      return 'default';
    }
  } else {
    console.log(`ℹ️ Permission already set to: ${Notification.permission}`);
    console.log(
      '💡 To see the dialog again, reset permissions in browser settings or use the reset function.'
    );
    return Notification.permission;
  }
}

/**
 * Show a test notification (for testing purposes)
 */
export async function showTestNotification(): Promise<void> {
  if (!isPushNotificationSupported()) return;

  if (getNotificationPermission() === 'granted') {
    new Notification('Test Notification', {
      body: 'This is a test notification from Auto Apply',
      icon: '/favicon.ico',
      badge: '/favicon.ico',
    });
  }
}
