/**
 * Feature Check Service
 *
 * This service provides methods for checking if a user has access to a feature
 * based on their subscription plan. It connects the plan features to feature checks.
 */

import { prisma } from './prisma';
import { FeatureAccessLevel } from '$lib/models/features/features';
import type { Feature } from '$lib/models/features/types';
import { getFeatureById } from '$lib/models/features/registry';

/**
 * Check if a user has access to a feature
 * @param userId The user ID
 * @param featureId The feature ID
 * @returns True if the user has access to the feature, false otherwise
 */
export async function hasFeatureAccess(userId: string, featureId: string): Promise<boolean> {
  try {
    console.log(`Checking feature access for user ${userId} and feature ${featureId}`);

    // Get the feature from the registry
    const feature = getFeatureById(featureId);
    if (!feature) {
      console.warn(`Feature not found: ${featureId}`);
      return false;
    }

    console.log(`Feature found: ${feature.name}`);

    // Get the user's subscription to determine the plan
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          include: {
            plan: {
              include: {
                features: true,
              },
            },
          },
        },
      },
    });

    // If the user doesn't exist, they don't have access
    if (!user) {
      console.warn(`User not found: ${userId}`);
      return false;
    }

    console.log(`User found: ${user.email}, role: ${user.role}`);

    // Get the current plan
    const currentPlan = user.subscriptions[0]?.plan;
    if (!currentPlan) {
      // If the user doesn't have a plan, check if they have the free role
      const hasAccess = user.role === 'free' && isFeatureInFreePlan(featureId);
      console.log(`User has no plan. Free role check: ${hasAccess}`);
      return hasAccess;
    }

    console.log(`User has plan: ${currentPlan.name}`);

    // Find the feature in the plan
    const planFeature = currentPlan.features.find((pf) => pf.featureId === featureId);
    if (!planFeature) {
      console.log(`Feature not found in plan ${currentPlan.name}`);
      return false;
    }

    console.log(`Feature found in plan with access level: ${planFeature.accessLevel}`);

    // Check if the feature is included in the plan
    const hasAccess = planFeature.accessLevel !== FeatureAccessLevel.NotIncluded;
    console.log(`Feature access result: ${hasAccess}`);
    return hasAccess;
  } catch (error) {
    console.error('Error checking feature access:', error);
    return false;
  }
}

/**
 * Check if a feature is in the free plan
 * This is a fallback for users who don't have a subscription
 * @param featureId The feature ID
 * @returns True if the feature is in the free plan, false otherwise
 */
function isFeatureInFreePlan(featureId: string): boolean {
  // Basic features that are available in the free plan
  // This is just a fallback - ideally features should come from the user's plan
  // We're being permissive here to ensure users can access basic functionality
  const freeFeatures = [
    'dashboard',
    'profile',
    'job_search_profiles',
    'job_search',
    // Add any other core features that should be available to all users
  ];
  console.log(`Checking if feature ${featureId} is in free plan`);
  return freeFeatures.includes(featureId);
}

/**
 * Get detailed feature access information for a user
 * @param userId The user ID
 * @param featureId The feature ID
 * @returns Detailed feature access information
 */
export async function getFeatureAccessDetails(
  userId: string,
  featureId: string
): Promise<{
  hasAccess: boolean;
  accessLevel?: FeatureAccessLevel;
  feature?: Feature;
  planFeature?: any;
  plan?: any;
  limits?: any[];
}> {
  try {
    // Get the feature from the registry
    const feature = getFeatureById(featureId);
    if (!feature) {
      console.warn(`Feature not found: ${featureId}`);
      return { hasAccess: false };
    }

    // Get the user's subscription to determine the plan
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          include: {
            plan: {
              include: {
                features: {
                  include: {
                    limits: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // If the user doesn't exist, they don't have access
    if (!user) {
      console.warn(`User not found: ${userId}`);
      return { hasAccess: false };
    }

    // Get the current plan
    const currentPlan = user.subscriptions[0]?.plan;
    if (!currentPlan) {
      // If the user doesn't have a plan, check if they have the free role
      const hasAccess = user.role === 'free' && isFeatureInFreePlan(featureId);
      return {
        hasAccess,
        accessLevel: hasAccess ? FeatureAccessLevel.Included : FeatureAccessLevel.NotIncluded,
        feature,
      };
    }

    // Find the feature in the plan
    const planFeature = currentPlan.features.find((pf) => pf.featureId === featureId);
    if (!planFeature) {
      return { hasAccess: false, feature, plan: currentPlan };
    }

    // Check if the feature is included in the plan
    const hasAccess = planFeature.accessLevel !== FeatureAccessLevel.NotIncluded;

    return {
      hasAccess,
      accessLevel: planFeature.accessLevel as FeatureAccessLevel,
      feature,
      planFeature,
      plan: currentPlan,
      limits: planFeature.limits,
    };
  } catch (error) {
    console.error('Error getting feature access details:', error);
    return { hasAccess: false };
  }
}

/**
 * Check if a user has reached their limit for a feature
 * @param userId The user ID
 * @param featureId The feature ID
 * @param limitId The limit ID
 * @returns True if the user has reached their limit, false otherwise
 */
export async function hasReachedLimit(
  userId: string,
  featureId: string,
  limitId: string
): Promise<boolean> {
  try {
    // Get feature access details
    const accessDetails = await getFeatureAccessDetails(userId, featureId);

    // If the user doesn't have access to the feature, they've reached the limit
    if (!accessDetails.hasAccess) {
      return true;
    }

    // If the feature is unlimited, the user hasn't reached the limit
    if (accessDetails.accessLevel === FeatureAccessLevel.Unlimited) {
      return false;
    }

    // If the feature is not limited, the user hasn't reached the limit
    if (accessDetails.accessLevel !== FeatureAccessLevel.Limited) {
      return false;
    }

    // Find the limit in the plan feature
    const limitValue = accessDetails.limits?.find((l) => l.limitId === limitId);
    if (!limitValue) {
      // If the limit isn't defined, assume the user has reached the limit
      return true;
    }

    // If the limit is unlimited, the user hasn't reached the limit
    if (limitValue.value === 'unlimited') {
      return false;
    }

    // Get the user's usage for this feature and limit
    const usage = await prisma.featureUsage.findUnique({
      where: {
        userId_featureId_limitId_period: {
          userId,
          featureId,
          limitId,
          period: getCurrentPeriod(),
        },
      },
    });

    // If there's no usage record, the user hasn't reached the limit
    if (!usage) {
      return false;
    }

    // Check if the user has reached their limit
    return usage.used >= parseInt(limitValue.value, 10);
  } catch (error) {
    console.error('Error checking if user has reached limit:', error);
    return true; // Assume the user has reached the limit if there's an error
  }
}

/**
 * Get the current period for monthly limits
 * @returns The current period in the format YYYY-MM
 */
function getCurrentPeriod(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
}
