<script lang="ts">
  import * as Switch from '$lib/components/ui/switch/index.js';
  import type { SuperForm } from 'sveltekit-superforms';
  import type { z } from 'zod';
  import type { notificationFormSchema } from '$lib/schemas/notification-schema';

  export let formData: SuperForm<z.infer<typeof notificationFormSchema>>;

  // Extract the form data store from the SuperForm
  const { form } = formData;

  // Function to trigger form change event
  function triggerFormChange() {
    // Dispatch a change event to the parent form
    const formElement = document.getElementById('notification-form');
    if (formElement) {
      formElement.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // Using direct form access with SuperForms
</script>

<!-- Marketing Communications -->
<div class="space-y-6">
  <!-- Marketing Emails -->
  <div class="flex items-center justify-between">
    <div class="space-y-0.5">
      <div class="font-medium">Marketing Emails</div>
      <div class="text-muted-foreground text-sm">Receive marketing and promotional emails</div>
    </div>
    <Switch.Root
      checked={Boolean($form.marketingEmails)}
      onCheckedChange={(checked) => {
        form.update((f) => ({ ...f, marketingEmails: checked }));
        triggerFormChange();
      }} />
  </div>

  <!-- Product Updates -->
  <div class="flex items-center justify-between">
    <div class="space-y-0.5">
      <div class="font-medium">Product Updates</div>
      <div class="text-muted-foreground text-sm">
        Receive notifications about new features and product updates
      </div>
    </div>
    <Switch.Root
      checked={Boolean($form.productUpdates)}
      onCheckedChange={(checked) => {
        form.update((f) => ({ ...f, productUpdates: checked }));
        triggerFormChange();
      }} />
  </div>

  <!-- Newsletter Subscription -->
  <div class="flex items-center justify-between">
    <div class="space-y-0.5">
      <div class="font-medium">Newsletter Subscription</div>
      <div class="text-muted-foreground text-sm">
        Receive our monthly newsletter with job search tips and industry insights
      </div>
    </div>
    <Switch.Root
      checked={Boolean($form.newsletterSubscription)}
      onCheckedChange={(checked) => {
        form.update((f) => ({ ...f, newsletterSubscription: checked }));
        triggerFormChange();
      }} />
  </div>

  <!-- Event Invitations -->
  <div class="flex items-center justify-between">
    <div class="space-y-0.5">
      <div class="font-medium">Event Invitations</div>
      <div class="text-muted-foreground text-sm">
        Receive invitations to webinars, career fairs, and other events
      </div>
    </div>
    <Switch.Root
      checked={Boolean($form.eventInvitations)}
      onCheckedChange={(checked) => {
        form.update((f) => ({ ...f, eventInvitations: checked }));
        triggerFormChange();
      }} />
  </div>
</div>
